import asyncio
import random
import re
import json
import logging
from playwright.async_api import async_playwright, Playwright, Route, TimeoutError as PlaywrightTimeoutError, Error as PlaywrightError

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 상수 및 설정
PROXY_USERNAME = 'ogh3113'
PROXY_PASSWORD = '@dlwnsdud0720'
PROXY_FILE = "proxy.txt"
UNFOUND_TARGETS_FILE = 'unfound_targets.json'
NAVER_MOBILE_URL = "https://m.naver.com"
SEARCH_INPUT_SELECTOR = '#MM_SEARCH_FAKE' # 네이버 모바일 검색창 셀렉터 (변경될 수 있음)
MORE_RESULTS_BUTTON_SELECTOR = 'a.link_feed_more, a.group_more[href^="https://m.search.naver.com/search.naver?nso="]' # '검색결과 더보기' 버튼 셀렉터 (변경될 수 있음) - 광고 링크 제외
PAGINATION_SELECTOR_TEMPLATE = 'a.pgn:has-text("{}")' # 페이지네이션 버튼 셀렉터 템플릿 (변경될 수 있음)
TARGET_URL_SELECTORS = [
    'a.link_source:not(.txt_link):not(:has(.icon_nad_mark))',
    'a.lnk_url:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.api_txt_lines a:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.total_wrap a:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.total_title a:not(.txt_link):not(:has(.icon_nad_mark))'
]
PAGE_LOAD_TIMEOUT = 10000 # 페이지 로드 타임아웃 (밀리초) - 10초로 단축

# 한국에서 많이 사용되는 최신 모바일 장치 설정 목록 (50개 수준의 다양한 환경 포함)
DEVICE_CONFIGS = [
    # Samsung Galaxy S 시리즈 (최신 및 이전 모델 포함)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S24 Ultra
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S921N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S24
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S918N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S23 Ultra
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S23
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-S908N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S22 Ultra
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-G991N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 780}}, # Galaxy S21
    {'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G981N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S20
    {'user_agent': 'Mozilla/5.0 (Linux; Android 10; SM-G973N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 760}}, # Galaxy S10

    # Samsung Galaxy Z 시리즈 (최신 모델 포함)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 412, 'height': 915}}, # Galaxy Z Fold 5 (Unfolded)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 412, 'height': 915}}, # Galaxy Z Flip 5
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-F936N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 412, 'height': 915}}, # Galaxy Z Fold 4 (Unfolded)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-F721N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 412, 'height': 915}}, # Galaxy Z Flip 4

    # Apple iPhone 시리즈 (최신 및 이전 모델 포함)
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1', 'viewport': {'width': 393, 'height': 852}}, # iPhone 15
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1', 'viewport': {'width': 430, 'height': 932}}, # iPhone 15 Pro Max
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1', 'viewport': {'width': 390, 'height': 844}}, # iPhone 14 Pro
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1', 'viewport': {'width': 428, 'height': 926}}, # iPhone 14 Plus
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Mobile/15E148 Safari/604.1', 'viewport': {'width': 375, 'height': 667}}, # iPhone SE (3rd gen)
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11

    # 네이버 앱 웹뷰 (다양한 OS 및 모델 조합)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 384, 'height': 854}}, # Android 14 Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 NAVER(inapp; search; 800; 11.8.0)', 'viewport': {'width': 360, 'height': 800}}, # Android 13 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 393, 'height': 852}}, # iPhone 17 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 11.9.0)', 'viewport': {'width': 390, 'height': 844}}, # iPhone 16 Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 412, 'height': 915}}, # Z Fold 5 Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 412, 'height': 915}}, # Z Flip 5 Naver In-app

    # 카카오톡 인앱 브라우저 (다양한 OS 및 모델 조합)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 384, 'height': 854}}, # Android 14 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 800}}, # Android 13 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 393, 'height': 852}}, # iPhone 17 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 390, 'height': 844}}, # iPhone 16 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 412, 'height': 915}}, # Z Fold 5 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 412, 'height': 915}}, # Z Flip 5 KakaoTalk In-app

    # 추가 조합 및 이전 OS 버전
    {'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G998N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.101 Mobile Safari/537.36', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S21 Ultra (Android 11)
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1', 'viewport': {'width': 390, 'height': 844}}, # iPhone 13 Pro (iOS 15)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 10; SM-A515N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 780}}, # Galaxy A51 (Android 10)
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1', 'viewport': {'width': 375, 'height': 812}}, # iPhone 11 Pro (iOS 14)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S901N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S23
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1', 'viewport': {'width': 390, 'height': 844}}, # iPhone 14 Pro (iOS 16)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-G998N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 NAVER(inapp; search; 800; 11.5.0)', 'viewport': {'width': 384, 'height': 854}}, # Android 12 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 11.0.0)', 'viewport': {'width': 390, 'height': 844}}, # iPhone 15 Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-G991N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 780}}, # Android 12 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 390, 'height': 844}}, # iPhone 15 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'viewport': {'width': 412, 'height': 915}}, # Galaxy S24 Ultra (wider viewport)
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1', 'viewport': {'width': 375, 'height': 812}}, # iPhone 15 (narrower viewport)
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 NAVER(inapp; search; 800; 11.8.0)', 'viewport': {'width': 412, 'height': 915}}, # Z Fold 4 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 428, 'height': 926}}, # iPhone 14 Plus KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S921N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S24 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1', 'viewport': {'width': 430, 'height': 932}}, # iPhone 15 Pro Max
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S918N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S23 Ultra KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 11.9.0)', 'viewport': {'width': 393, 'height': 852}}, # iPhone 14 Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-A528N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36 NAVER(inapp; search; 800; 11.2.3)', 'viewport': {'width': 360, 'height': 780}}, # Galaxy A52s Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 375, 'height': 667}}, # iPhone SE (2nd/3rd gen) KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-F721N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 412, 'height': 915}}, # Z Flip 4 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 11.0.0)', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11/XR Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 10; SM-G973N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36 NAVER(inapp; search; 800; 10.0.0)', 'viewport': {'width': 360, 'height': 760}}, # Galaxy S10 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11/XR KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G981N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S20 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 430, 'height': 932}}, # iPhone 15 Pro Max KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 412, 'height': 915}}, # Z Fold 5 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 412, 'height': 915}}, # Z Flip 5 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 11.8.0)', 'viewport': {'width': 428, 'height': 926}}, # iPhone 14 Plus Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S908N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S22 Ultra KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-G973N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.88 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 760}}, # Galaxy S10 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 10.0.0)', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11 Pro Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 10; SM-A515N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 780}}, # Galaxy A51 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 390, 'height': 844}}, # iPhone 14 Pro (iOS 16) KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G981N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36 NAVER(inapp; search; 800; 11.0.0)', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S20 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 12.0.0)', 'viewport': {'width': 430, 'height': 932}}, # iPhone 15 Pro Max Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-S908N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S22 Ultra KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 393, 'height': 852}}, # iPhone 14 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 13; SM-S918N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 NAVER(inapp; search; 800; 11.8.0)', 'viewport': {'width': 384, 'height': 854}}, # Galaxy S23 Ultra Naver In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 14; SM-S921N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S24 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 428, 'height': 926}}, # iPhone 14 Plus KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 12; SM-A528N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 780}}, # Galaxy A52s KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1 KAKAOTALK', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11/XR KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 10; SM-G973N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36 KAKAOTALK', 'viewport': {'width': 360, 'height': 760}}, # Galaxy S10 KakaoTalk In-app
    {'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G981N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36 NAVER(inapp; search; 800; 11.0.0)', 'viewport': {'width': 360, 'height': 800}}, # Galaxy S20 Naver In-app
    {'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 NAVER(inapp; search; 800; 10.0.0)', 'viewport': {'width': 414, 'height': 896}}, # iPhone 11 Pro Naver In-app
]

# 검색 설정 (키워드 및 도메인 쌍 목록)
SEARCH_TARGETS = [
    {'keyword': '운암자이포레나', 'domain': 'aryatps.com'},
    {'keyword': '운암자이', 'domain': 'aryatps.com'},
    {'keyword': '일곡공원 위파크 1644-7240', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '일곡위파크', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '장성 삼계 서현위드184', 'domain': 'damyangkoreaadelium.quv.kr'},
    {'keyword': '장성 바울루체', 'domain': 'immodelhouse10.quv.kr'},
    {'keyword': '대전 엘크루', 'domain': 'immodelhouse3.quv.kr'},
    {'keyword': '정읍 월드메르디앙', 'domain': 'modelhouse7l7.quv.kr'},
    {'keyword': '송암공원 중흥s클래스', 'domain': 'immodelhouse98.quv.kr'},
    {'keyword': '중앙공원 롯데캐슬', 'domain': 'immodelhouse2. quv.kr'},
    {'keyword': '함평 미래프레지안', 'domain': 'modelhouse1d.quv.kr'},
    {'keyword': '봉선 이편한세상', 'domain': 'modelhouse1b.quv.kr'},
    {'keyword': '함평 서현 수와일 리버파크', 'domain': 'immodelhouse7.quv.kr'},
    {'keyword': '동림 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '동림2차 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '무등산자이앤어울림', 'domain': 'kingofyeosuhonors.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '더샵광양레이크센텀', 'domain': 'immodelhouse1.quv.kr'},
    {'keyword': '상무 스위첸', 'domain': 'immodelhouse81.quv.kr'},
    {'keyword': '마포 빌리브디에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '마포빌리브에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '상무 모아미래도', 'domain': 'immodelhouse93.quv.kr'},
    {'keyword': '순천 더포레스트 마루힐', 'domain': 'immodelhouseb.quv.kr'},
    {'keyword': '무인카페 스타벅스', 'domain': 'starbuckskorea.quv.kr'},
    {'keyword': '중외공원 힐스테이트 공식1644-7240', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '힐스테이트 중외공원 공식', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '순천 지에이그린웰 예약', 'domain': 'immodelhouse90.quv.kr'},
    {'keyword': '선운2지구 예다음', 'domain': 'goldmodelhouse.quv.kr'},
    {'keyword': '아크로베스티뉴 공식', 'domain': 'inmodelhouse.quv.kr'},
    {'keyword': '광주 한양더힐 공식', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '각화 한양더힐1644-7240', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '월산 힐스테이트1644 7240', 'domain': 'immodelhouse4.quv.kr'},
    {'keyword': '중앙공원 위파크', 'domain': 'immodelhouse99.quv.kr'},
    {'keyword': '화정 두산위브 모델하우스', 'domain': 'modelhouse1a.quv.kr'},
    {'keyword': '익산역 유탑유블레스', 'domain': 'modelhouse1e.quv.kr'},
    {'keyword': '진월 더리브 라포레', 'domain': 'iamodelhome.quv.kr'},
    {'keyword': '무등산 우방아이유쉘', 'domain': 'thesynergy.quv.kr'},
    {'keyword': '힐스테이트 천호역1644-7240', 'domain': 'immodelhouse91.quv.kr'}
]

# 프록시 목록 로드 함수
async def load_proxies(file_path: str) -> list[str]:
    """
    지정된 파일에서 프록시 목록을 읽어옵니다.

    Args:
        file_path: 프록시 목록 파일 경로.

    Returns:
        프록시 목록 (list of str). 파일을 찾을 수 없거나 읽기 오류 발생 시 빈 리스트 반환.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip()]
        logging.info(f"{len(proxies)}개의 프록시를 '{file_path}'에서 로드했습니다.")
        return proxies
    except FileNotFoundError:
        logging.error(f"오류: '{file_path}' 파일을 찾을 수 없습니다.")
        return []
    except Exception as e:
        logging.error(f"프록시 로드 중 오류 발생: {e}")
        return []

# 사람처럼 보이게 하는 비동기 지연 함수
async def human_like_delay(min_seconds: float = 1, max_seconds: float = 5):
    """
    지정된 범위 내에서 무작위 시간만큼 비동기적으로 대기합니다.

    Args:
        min_seconds: 최소 대기 시간 (초).
        max_seconds: 최대 대기 시간 (초).
    """
    delay_time = random.uniform(min_seconds, max_seconds)
    logging.debug(f"'{delay_time:.2f}'초 동안 대기합니다.")
    await asyncio.sleep(delay_time)

# 무작위 스크롤 함수
async def random_scroll(page):
    """
    페이지를 무작위 양만큼 스크롤합니다.
    """
    scroll_amount = random.randint(200, 800) # 무작위 스크롤 양
    scroll_duration = random.uniform(0.5, 2.0) # 무작위 스크롤 시간
    logging.debug(f"'{scroll_amount}' 픽셀만큼 스크롤하고 '{scroll_duration:.2f}'초 대기합니다.")
    await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
    await asyncio.sleep(scroll_duration)

# 목표 URL 찾기 함수
async def find_target_url(page, domain: str) -> str | None:
    """
    현재 페이지에서 주어진 도메인을 포함하는 목표 URL을 찾습니다.

    Args:
        page: Playwright Page 객체.
        domain: 찾을 도메인 문자열.

    Returns:
        찾은 목표 URL (str) 또는 찾지 못한 경우 None.
    """
    logging.debug(f"도메인 '{domain}'을 포함하는 목표 URL 검색 시작.")
    for selector in TARGET_URL_SELECTORS:
        try:
            # query_selector_all은 요소가 없어도 예외를 발생시키지 않음
            links = await page.query_selector_all(selector)
            for link in links:
                href = await link.get_attribute('href')
                if href and domain in href:
                    logging.info(f"목표 URL 발견: '{href}' (셀렉터: '{selector}')")
                    return href
        except PlaywrightError as e:
            # Playwright 관련 오류 발생 시 로깅
            logging.warning(f"셀렉터 '{selector}' 처리 중 Playwright 오류 발생: {e}")
            continue # 다음 셀렉터로 계속 진행
        except Exception as e:
            # 그 외 예상치 못한 오류 발생 시 로깅
            logging.warning(f"셀렉터 '{selector}' 처리 중 예상치 못한 오류 발생: {e}")
            continue # 다음 셀렉터로 계속 진행


    logging.debug(f"도메인 '{domain}'을 포함하는 목표 URL을 찾지 못했습니다.")
    return None

# 네이버 검색 실행 및 목표 URL 찾기
async def search_naver(page, keyword: str, domain: str) -> str | None:
    """
    네이버에서 키워드를 검색하고 목표 도메인을 포함하는 URL을 찾습니다.

    Args:
        page: Playwright Page 객체.
        keyword: 검색할 키워드.
        domain: 찾을 목표 도메인.

    Returns:
        찾은 목표 URL (str) 또는 찾지 못한 경우 None.
    """
    logging.info(f"'{keyword}' 검색어와 도메인 '{domain}'으로 검색 시작")
    try:
        # 1. 검색창에 키워드 입력 및 검색 실행
        logging.info(f"검색어 '{keyword}' 입력 및 검색 실행 시도")
        try:
            await page.fill(SEARCH_INPUT_SELECTOR, keyword)
            await human_like_delay(1, 2)
            await page.press(SEARCH_INPUT_SELECTOR, 'Enter')
            await page.wait_for_load_state('networkidle', timeout=PAGE_LOAD_TIMEOUT)
            await human_like_delay()
            await random_scroll(page) # 검색 결과 페이지 로드 후 스크롤
            logging.info("검색 실행 및 페이지 로드 완료.")
        except PlaywrightTimeoutError:
            logging.warning(f"검색 결과 페이지 로드 시간 초과 (TimeoutError). 키워드: '{keyword}'")
            return None # 시간 초과 시 해당 검색 건너뛰기
        except PlaywrightError as e:
            logging.error(f"검색 실행 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
            return None # Playwright 오류 발생 시 해당 검색 건너뛰기
        except Exception as e:
            logging.error(f"검색 실행 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
            return None # 예상치 못한 오류 발생 시 해당 검색 건너뛰기


        # 2. 1페이지에서 목표 URL 찾기
        logging.info("1페이지에서 목표 URL 검색")
        target_url = await find_target_url(page, domain)
        if target_url:
            logging.info(f"1페이지에서 목표 URL 발견: {target_url}")
            # 목표 URL 방문
            logging.info(f"목표 URL 방문: {target_url}")
            try:
                await human_like_delay(2, 5) # 방문 전 지연
                await page.goto(target_url, wait_until='networkidle', timeout=PAGE_LOAD_TIMEOUT)
                await human_like_delay(5, 10) # 방문 후 지연
                logging.info("목표 URL 방문 완료.")
                # TODO: 목표 페이지 탐색 로직 추가 (필요시)
                return target_url
            except PlaywrightTimeoutError:
                logging.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
                return target_url # URL은 찾았으므로 반환 (방문 실패는 별도 처리 필요시 수정)
            except PlaywrightError as e:
                logging.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
                return target_url # URL은 찾았으므로 반환
            except Exception as e:
                logging.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
                return target_url # URL은 찾았으므로 반환


        # 3. '검색결과 더보기' 버튼 클릭 시도 및 2페이지 검색
        logging.info("'검색결과 더보기' 버튼 검색")
        more_results_button = await page.query_selector(MORE_RESULTS_BUTTON_SELECTOR)
        if more_results_button:
            logging.info("'검색결과 더보기' 버튼 클릭 시도")
            try:
                await more_results_button.click()
                await page.wait_for_load_state('networkidle', timeout=PAGE_LOAD_TIMEOUT)
                await human_like_delay()
                await random_scroll(page) # '더보기' 클릭 후 스크롤
                logging.info("'검색결과 더보기' 클릭 및 2페이지 로드 완료.")

                logging.info("2페이지에서 목표 URL 검색")
                # 목표 URL 링크 중 하나라도 나타날 때까지 기다립니다.
                try:
                    await page.wait_for_selector(", ".join(TARGET_URL_SELECTORS), timeout=PAGE_LOAD_TIMEOUT)
                    logging.info("2페이지에서 목표 URL 링크 요소 확인됨.")
                except PlaywrightTimeoutError:
                    logging.warning(f"2페이지에서 목표 URL 링크 요소 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                    # 시간 초과 시 목표 URL을 찾지 못한 것으로 간주하고 다음 단계로 이동
                    pass
                except PlaywrightError as e:
                    logging.warning(f"2페이지에서 목표 URL 링크 요소 대기 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                    # 오류 발생 시 목표 URL을 찾지 못한 것으로 간주하고 다음 단계로 이동
                    pass
                except Exception as e:
                    logging.warning(f"2페이지에서 목표 URL 링크 요소 대기 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                    # 오류 발생 시 목표 URL을 찾지 못한 것으로 간주하고 다음 단계로 이동
                    pass

                target_url = await find_target_url(page, domain)
                if target_url:
                    logging.info(f"2페이지에서 목표 URL 발견: {target_url}")
                    # 목표 URL 방문
                    logging.info(f"목표 URL 방문: {target_url}")
                    try:
                        await human_like_delay(2, 5) # 방문 전 지연
                        await page.goto(target_url, wait_until='networkidle', timeout=PAGE_LOAD_TIMEOUT)
                        await human_like_delay(5, 10) # 방문 후 지연
                        logging.info("목표 URL 방문 완료.")
                        # TODO: 목표 페이지 탐색 로직 추가 (필요시)
                        return target_url
                    except PlaywrightTimeoutError:
                        logging.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
                        return target_url # URL은 찾았으므로 반환 (방문 실패는 별도 처리 필요시 수정)
                    except PlaywrightError as e:
                        logging.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
                        return target_url # URL은 찾았으므로 반환
                    except Exception as e:
                        logging.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
                        return target_url # URL은 찾았으므로 반환

            except PlaywrightTimeoutError:
                logging.warning(f"'검색결과 더보기' 클릭 후 2페이지 로드 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                # 시간 초과 시 페이지네이션으로 계속 시도
            except PlaywrightError as e:
                logging.warning(f"'검색결과 더보기' 클릭 또는 2페이지 검색 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                # 오류 발생 시 페이지네이션으로 계속 시도
            except Exception as e:
                logging.warning(f"'검색결과 더보기' 클릭 또는 2페이지 검색 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                # 오류 발생 시 페이지네이션으로 계속 시도
        else:
             logging.info("'검색결과 더보기' 버튼을 찾을 수 없습니다. 페이지네이션을 사용하여 2페이지로 이동 시도.")
             # '검색결과 더보기' 버튼을 찾지 못한 경우, 페이지네이션을 사용하여 2페이지로 이동 시도
             pagination_selector_2 = PAGINATION_SELECTOR_TEMPLATE.format(2)
             pagination_button_2 = await page.query_selector(pagination_selector_2)

             if pagination_button_2:
                 try:
                     await pagination_button_2.click()
                     await page.wait_for_load_state('networkidle', timeout=PAGE_LOAD_TIMEOUT)
                     await human_like_delay()
                     await random_scroll(page) # 페이지 이동 후 스크롤
                     logging.info("페이지네이션을 사용하여 2페이지 로드 완료.")

                     logging.info("2페이지에서 목표 URL 검색")
                     # 목표 URL 링크 중 하나라도 나타날 때까지 기다립니다.
                     try:
                         await page.wait_for_selector(", ".join(TARGET_URL_SELECTORS), timeout=PAGE_LOAD_TIMEOUT)
                         logging.info("2페이지에서 목표 URL 링크 요소 확인됨.")
                     except PlaywrightTimeoutError:
                         logging.warning(f"2페이지에서 목표 URL 링크 요소 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                         pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)
                     except PlaywrightError as e:
                         logging.warning(f"2페이지에서 목표 URL 링크 요소 대기 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                         pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)
                     except Exception as e:
                         logging.warning(f"2페이지에서 목표 URL 링크 요소 대기 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                         pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)

                     target_url = await find_target_url(page, domain)
                     if target_url:
                         logging.info(f"2페이지에서 목표 URL 발견: {target_url}")
                         # 목표 URL 방문
                         logging.info(f"목표 URL 방문: {target_url}")
                         try:
                             await human_like_delay(2, 5) # 방문 전 지연
                             await page.goto(target_url, wait_until='networkidle', timeout=PAGE_LOAD_TIMEOUT)
                             await human_like_delay(5, 10) # 방문 후 지연
                             logging.info("목표 URL 방문 완료.")
                             # TODO: 목표 페이지 탐색 로직 추가 (필요시)
                             return target_url
                         except PlaywrightTimeoutError:
                             logging.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
                             return target_url
                         except PlaywrightError as e:
                             logging.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
                             return target_url
                         except Exception as e:
                             logging.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
                             return target_url

                 except PlaywrightTimeoutError:
                     logging.warning(f"페이지네이션을 사용한 2페이지 로드 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                     # 시간 초과 시 페이지네이션으로 계속 시도 (3페이지부터)
                 except PlaywrightError as e:
                     logging.warning(f"페이지네이션을 사용한 2페이지 이동 또는 검색 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                     # 오류 발생 시 페이지네이션으로 계속 시도 (3페이지부터)
                 except Exception as e:
                     logging.warning(f"페이지네이션을 사용한 2페이지 이동 또는 검색 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                     # 오류 발생 시 페이지네이션으로 계속 시도 (3페이지부터)
             else:
                 logging.info("2페이지 페이지네이션 버튼을 찾을 수 없습니다.")


        # 4. 3~10 페이지네이션 탐색
        for i in range(3, 11):
            logging.info(f"{i} 페이지로 이동 시도")
            pagination_selector = PAGINATION_SELECTOR_TEMPLATE.format(i)
            pagination_button = await page.query_selector(pagination_selector)

            if pagination_button:
                try:
                    await pagination_button.click()
                    await page.wait_for_load_state('networkidle', timeout=PAGE_LOAD_TIMEOUT)
                    await human_like_delay()
                    await random_scroll(page) # 페이지 이동 후 스크롤
                    logging.info(f"{i} 페이지 로드 완료.")

                    # 목표 URL 링크 중 하나라도 나타날 때까지 기다립니다.
                    try:
                        await page.wait_for_selector(", ".join(TARGET_URL_SELECTORS), timeout=PAGE_LOAD_TIMEOUT)
                        logging.info(f"{i} 페이지에서 목표 URL 링크 요소 확인됨.")
                    except PlaywrightTimeoutError:
                        logging.warning(f"{i} 페이지에서 목표 URL 링크 요소 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                        pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)
                    except PlaywrightError as e:
                        logging.warning(f"{i} 페이지에서 목표 URL 링크 요소 대기 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                        pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)
                    except Exception as e:
                        logging.warning(f"{i} 페이지에서 목표 URL 링크 요소 대기 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                        pass # 다음 로직으로 이동하여 find_target_url 호출 (None 반환 예상)


                    target_url = await find_target_url(page, domain)
                    if target_url:
                        logging.info(f"{i} 페이지에서 목표 URL 발견: {target_url}")
                        # 목표 URL 방문
                        logging.info(f"목표 URL 방문: {target_url}")
                        try:
                            await human_like_delay(2, 5) # 방문 전 지연
                            await page.goto(target_url, wait_until='networkidle', timeout=PAGE_LOAD_TIMEOUT)
                            await human_like_delay(5, 10) # 방문 후 지연
                            logging.info("목표 URL 방문 완료.")
                            # TODO: 목표 페이지 탐색 로직 추가 (필요시)
                            return target_url
                        except PlaywrightTimeoutError:
                            logging.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
                            return target_url
                        except PlaywrightError as e:
                            logging.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
                            return target_url
                        except Exception as e:
                            logging.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
                            return target_url

                except PlaywrightTimeoutError:
                    logging.warning(f"{i} 페이지 로드 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                    # 시간 초과 시 다음 페이지로 계속 시도
                except PlaywrightError as e:
                    logging.warning(f"{i} 페이지 이동 또는 검색 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                    # 특정 페이지 오류 시 다음 페이지로 계속 시도
                except Exception as e:
                    logging.warning(f"{i} 페이지 이동 또는 검색 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                    # 특정 페이지 오류 시 다음 페이지로 계속 시도
            else:
                logging.info(f"{i} 페이지 버튼을 찾을 수 없습니다. 페이지네이션 종료.")
                break # 페이지네이션 버튼이 없으면 종료

        logging.info(f"'{keyword}' 검색어와 도메인 '{domain}'에 대한 목표 URL을 찾지 못했습니다.")
        return None

    except Exception as e:
        # 검색 과정 전반에서 발생할 수 있는 치명적인 예외 처리
        logging.error(f"'{keyword}' 검색어와 도메인 '{domain}' 검색 중 치명적인 오류 발생: {e}")
        return None

# 찾지 못한 검색 설정 저장 함수
def save_unfound_targets(unfound_targets: list[dict], file_path: str):
    """
    목표 URL을 찾지 못한 검색 설정을 JSON 파일로 저장합니다.

    Args:
        unfound_targets: 찾지 못한 검색 설정 목록.
        file_path: 저장할 JSON 파일 경로.
    """
    if unfound_targets:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(unfound_targets, f, ensure_ascii=False, indent=4)
            logging.info(f"목표 URL을 찾지 못한 검색 설정 {len(unfound_targets)}개를 '{file_path}' 파일에 저장했습니다.")
        except Exception as e:
            logging.error(f"찾지 못한 검색 설정 저장 중 오류 발생: {e}")
    else:
        logging.info("목표 URL을 찾지 못한 검색 설정이 없습니다.")

# 메인 실행 함수
async def main():
    """
    메인 실행 함수. 프록시를 순회하며 검색 작업을 수행합니다.
    """
    proxies = await load_proxies(PROXY_FILE)
    if not proxies:
        logging.error("프록시 목록이 비어 있습니다. 프로그램을 종료합니다.")
        return

    unfound_targets = [] # 목표 URL을 찾지 못한 검색 설정을 저장할 리스트

    async with async_playwright() as p:
        for proxy in proxies:
            logging.info(f"프록시 사용 시작: {proxy}")
            proxy_config = {
                'server': proxy,
                'username': PROXY_USERNAME,
                'password': PROXY_PASSWORD
            }

            browser = None
            try:
                # 사람처럼 보이게 하기 위한 추가 설정
                browser = await p.chromium.launch(
                    headless=False, # 개발 중에는 False, 배포 시 True
                    proxy=proxy_config, # type: ignore
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-blink-features=AutomationControlled' # 자동화 감지 우회
                    ]
                )
                # 무작위 장치 설정 선택
                random_device = random.choice(DEVICE_CONFIGS)
                logging.info(f"선택된 장치 설정: User-Agent='{random_device['user_agent']}', Viewport='{random_device['viewport']}'")

                context = await browser.new_context(
                    user_agent=random_device['user_agent'], # 무작위 사용자 에이전트 사용
                    viewport=random_device['viewport'], # 무작위 뷰포트 사용
                    locale='ko-KR',
                    permissions=['geolocation'], # 필요한 권한 설정
                    ignore_https_errors=True # HTTPS 오류 무시 (필요시)
                )
                page = await context.new_page()

                # 불필요한 리소스 차단 핸들러
                async def handle_route(route: Route):
                    """
                    특정 리소스 타입의 요청을 차단합니다.
                    """
                    if route.request.resource_type in ["image", "media", "stylesheet", "font"]:
                        await route.abort()
                    else:
                        await route.continue_()

                # 불필요한 리소스 (이미지, 미디어, 스타일시트, 폰트 등) 차단
                await page.route('**/*', handle_route)

                # 자동화 감지 추가 우회 스크립트 주입
                await page.add_init_script("Object.defineProperty(navigator, 'webdriver', 'undefined')") # 수정: get: () => undefined 대신 'undefined' 사용

                # 네이버 모바일 페이지로 이동 (프록시별로 새로 시작)
                logging.info(f"네이버 모바일 페이지 '{NAVER_MOBILE_URL}'로 이동 시도")
                try:
                    await page.goto(NAVER_MOBILE_URL, timeout=PAGE_LOAD_TIMEOUT, wait_until='networkidle')
                    await human_like_delay()
                    logging.info("네이버 모바일 페이지 로드 완료.")
                except PlaywrightTimeoutError:
                    logging.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError). 프록시: '{proxy}'")
                    await browser.close() # 현재 브라우저 닫고 다음 프록시로 이동
                    logging.info(f"프록시 사용 종료: {proxy}")
                    await human_like_delay(10, 20) # 프록시 변경 후 충분한 지연
                    continue # 다음 프록시로 이동
                except PlaywrightError as e:
                    logging.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e}. 프록시: '{proxy}'")
                    await browser.close() # 현재 브라우저 닫고 다음 프록시로 이동
                    logging.info(f"프록시 사용 종료: {proxy}")
                    await human_like_delay(10, 20) # 프록시 변경 후 충분한 지연
                    continue # 다음 프록시로 이동
                except Exception as e:
                    logging.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e}. 프록시: '{proxy}'")
                    await browser.close() # 현재 브라우저 닫고 다음 프록시로 이동
                    logging.info(f"프록시 사용 종료: {proxy}")
                    await human_like_delay(10, 20) # 프록시 변경 후 충분한 지연
                    continue # 다음 프록시로 이동


                # SEARCH_TARGETS의 순서를 섞음 (원본 리스트를 변경하지 않도록 copy)
                search_targets_shuffled = SEARCH_TARGETS.copy()
                random.shuffle(search_targets_shuffled)

                for target in search_targets_shuffled:
                    keyword = target.get('keyword', '')
                    domain = target.get('domain', '')
                    if not keyword or not domain:
                        logging.warning(f"유효하지 않은 검색 설정 발견: {target}. 건너뜁니다.")
                        continue

                    # 각 검색 작업 전에 네이버 모바일 페이지로 이동하여 초기화
                    logging.info(f"새 작업 시작: 키워드 '{keyword}', 도메인 '{domain}'. 네이버 모바일 페이지로 이동하여 초기화.")
                    try:
                        await page.goto(NAVER_MOBILE_URL, timeout=PAGE_LOAD_TIMEOUT, wait_until='networkidle')
                        await human_like_delay()
                        logging.info("네이버 모바일 페이지 로드 완료.")
                    except PlaywrightTimeoutError:
                        logging.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError). 키워드: '{keyword}'")
                        unfound_targets.append(target) # 오류 발생 시 리스트에 추가
                        continue # 다음 검색 타겟으로 이동
                    except PlaywrightError as e:
                        logging.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
                        unfound_targets.append(target) # 오류 발생 시 리스트에 추가
                        continue # 다음 검색 타겟으로 이동
                    except Exception as e:
                        logging.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
                        unfound_targets.append(target) # 오류 발생 시 리스트에 추가
                        continue # 다음 검색 타겟으로 이동


                    # 검색 실행 및 목표 URL 찾기 (search_naver는 이제 찾았는지 여부만 반환)
                    # search_naver 함수 내에서 목표 URL을 찾으면 해당 URL로 이동까지 완료합니다.
                    found = await search_naver(page, keyword, domain)

                    if found:
                        logging.info(f"성공: 키워드 '{keyword}', 도메인 '{domain}'에 대한 목표 URL 발견 및 방문 완료.")
                        # TODO: 목표 페이지 탐색 로직 추가 (필요시)
                        # 목표 URL 방문 후에는 다음 검색을 위해 다시 네이버 초기 페이지로 돌아가야 합니다.
                        logging.info(f"다음 검색을 위해 네이버 모바일 페이지 '{NAVER_MOBILE_URL}'로 이동.")
                        try:
                            await page.goto(NAVER_MOBILE_URL, timeout=PAGE_LOAD_TIMEOUT, wait_until='networkidle')
                            await human_like_delay()
                            logging.info("네이버 모바일 페이지 로드 완료.")
                        except PlaywrightTimeoutError:
                            logging.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError) 후 목표 URL 방문. 키워드: '{keyword}'")
                            # 이 경우 다음 검색 타겟 처리에 문제가 발생할 수 있으므로 현재 프록시에서의 검색 작업 중단
                            break # 현재 프록시에서의 검색 작업 중단
                        except PlaywrightError as e:
                            logging.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e} 후 목표 URL 방문. 키워드: '{keyword}'")
                            break # 현재 프록시에서의 검색 작업 중단
                        except Exception as e:
                            logging.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e} 후 목표 URL 방문. 키워드: '{keyword}'")
                            break # 현재 프록시에서의 검색 작업 중단

                    else:
                        logging.warning(f"실패: 키워드 '{keyword}', 도메인 '{domain}'에 대한 목표 URL을 찾지 못했습니다.")
                        unfound_targets.append(target) # 찾지 못한 경우 리스트에 추가
                        # 다음 검색 타겟을 위해 현재 페이지(검색 결과 페이지)에서 네이버 초기 페이지로 돌아가야 합니다.
                        logging.info(f"다음 검색을 위해 네이버 모바일 페이지 '{NAVER_MOBILE_URL}'로 이동.")
                        try:
                            await page.goto(NAVER_MOBILE_URL, timeout=PAGE_LOAD_TIMEOUT, wait_until='networkidle')
                            await human_like_delay()
                            logging.info("네이버 모바일 페이지 로드 완료.")
                        except PlaywrightTimeoutError:
                            logging.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError) 후 목표 URL 미발견. 키워드: '{keyword}'")
                            break # 현재 프록시에서의 검색 작업 중단
                        except PlaywrightError as e:
                            logging.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e} 후 목표 URL 미발견. 키워드: '{keyword}'")
                            break # 현재 프록시에서의 검색 작업 중단
                        except Exception as e:
                            logging.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e} 후 목표 URL 미발견. 키워드: '{keyword}'")
                            break # 현재 프록시에서의 검색 작업 중단


                    await human_like_delay(5, 10) # 각 검색 작업 후 충분한 지연

            except Exception as e:
                # 프록시 사용 중 브라우저/컨텍스트 설정 또는 기타 예외 발생 시
                logging.error(f"프록시 '{proxy}' 사용 중 치명적인 오류 발생: {e}")
                # 현재 프록시에서 오류 발생 시 다음 프록시로 이동

            finally:
                if browser:
                    await browser.close()
                    logging.info(f"프록시 사용 종료: {proxy}")

            # 프록시 변경 후 충분한 지연
            await human_like_delay(10, 20)

    # 모든 작업 완료 후 찾지 못한 검색 설정 파일로 저장
    save_unfound_targets(unfound_targets, UNFOUND_TARGETS_FILE)

if __name__ == "__main__":
    asyncio.run(main())
