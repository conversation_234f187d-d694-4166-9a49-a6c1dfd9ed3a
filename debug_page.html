<!DOCTYPE html><html lang="ko" data-useragent="mozilla/5.0 (iphone; cpu iphone os 17_4 like mac os x) applewebkit/605.1.15 (khtml, like gecko) version/17.4 mobile/15e148 safari/604.1" data-platform="win32"><head> <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> <meta http-equiv="Content-Script-Type" content="text/javascript"> <meta name="referrer" content="always"> <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, minimum-scale=1.0, user-scalable=yes"> <meta name="format-detection" content="telephone=no,address=no,email=no"> <meta property="og:title" content="중앙공원 롯데캐슬 : 네이버 검색"> <meta property="og:image" content="https://ssl.pstatic.net/sstatic/search/favicon/og_200x200_240820.png"> <meta property="og:description" content="'중앙공원 롯데캐슬'의 네이버 검색 결과입니다."> <meta name="description" lang="ko" content="'중앙공원 롯데캐슬'의 네이버 검색 결과입니다."> <title>중앙공원 롯데캐슬 : 네이버 검색</title> <link rel="shortcut icon" href="https://ssl.pstatic.net/sstatic/search/favicon/favicon_32x32_240820.ico"> <link rel="apple-touch-icon" sizes="180x180" href="https://ssl.pstatic.net/sstatic/search/favicon/apple-touch-icon_180x180_240820.png"> <link rel="apple-touch-icon" sizes="120x120" href="https://ssl.pstatic.net/sstatic/search/favicon/apple-touch-icon_120x120_240820.png"> <link rel="apple-touch-icon" sizes="114x114" href="https://ssl.pstatic.net/sstatic/search/favicon/apple-touch-icon_114x114_240820.png"> <link rel="apple-touch-icon" href="https://ssl.pstatic.net/sstatic/search/favicon/apple-touch-icon_57x57_240820.png"> <style type="text/css"> noscript_disp { display: none; } </style><link rel="stylesheet" type="text/css" href="https://ssl.pstatic.net/sstatic/search/mobile/css/w_new_250522.css"> <noscript> <style type="text/css"> noscript_disp { display: block; } </style> </noscript><script async="" src="https://ntm.pstatic.net/ex/nlog.js"></script><script async="" src="https://ntm.pstatic.net/scripts/ntm_ec8638b0efc1.js"></script><script> naver = window.naver || {}; naver.search = naver.search || {}; naver.search.abt_param = ""; </script><script> naver = window.naver || {}; naver.search = naver.search || {}; naver.search.error = (function () { var errorList = Array() ; return { add : function (s) { errorList.push(s) ; }, clear : function () { delete errorList ; }, get : function (s) { return errorList ; }, getString : function (d) { if (typeof d === 'undefined') d = '|' ; return errorList.join(d) ; } } })(); function nx_add_event_handler (event, func) { $Fn(func).attach(document, event) ; } function bt(id, after) { document.getElementById(id).src = after; } function document_write (s) { document.write(s) ; } </script><script> var g_nx_app_next_button = 0 ; function openRTKeyword () { window.location.href = "naversearchapp://newsearch?action=ranklist"; } </script><script> if (!String.prototype.trim) { String.prototype.trim = function () { return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, ''); }; } if (!Array.prototype.indexOf) { Array.prototype.indexOf = function(searchElement, fromIndex) { var k; if (this == null) { throw new TypeError('"this" is null or not defined'); } var o = Object(this); var len = o.length >>> 0; if (len === 0) { return -1; } var n = fromIndex | 0; if (n >= len) { return -1; } k = Math.max(n >= 0 ? n : len - Math.abs(n), 0); while (k < len) { if (k in o && o[k] === searchElement) { return k; } k++; } return -1; }; } if (!Array.prototype.filter) { Array.prototype.filter = function(func, thisArg) { 'use strict'; if (!((typeof func === 'Function' || typeof func === 'function') && this)) throw new TypeError(); var len = this.length >>> 0, res = new Array(len), t = this, c = 0, i = -1; var kValue; if (thisArg === undefined) { while (++i !== len) { if (i in this) { kValue = t[i]; if (func(t[i], i, t)) { res[c++] = kValue; } } } } else { while (++i !== len) { if (i in this) { kValue = t[i]; if (func.call(thisArg, t[i], i, t)) { res[c++] = kValue; } } } } res.length = c; return res; }; } if (typeof(encodeURIComponent) != "function") { encodeURIComponent = function (s) { function toHex (n) { var hexchars = "0123456789ABCDEF" ; return "%" + hexchars.charAt(n>>4) + hexchars.charAt(n&0xF) ; } var es = "" ; for (var i = 0; i < s.length;) { var c = s.charCodeAt(i++) ; if ((c&0xF800) == 0xD800) { var sc = s.charCodeAt(i++) ; c = ((c-0xD800)<<10) + (sc-0xDC00) + 0x10000 ; } if (!(c&~0x7F)) { if ((c>=65&&c<=90) || (c>=97&&c<=122) || (c>=48&&c<=57) || (c>=45&&c<=46) || c==95 || c==33 || c==126 || (c>=39&&c<=42)) es += String.fromCharCode(c) ; else es += toHex(c) ; } else if (!(c&~0x7FF)) es += toHex(0xC0+(c>>6)) + toHex(c&0x3F) ; else if (!(c&~0xFFFF)) es += toHex(0xE0+(c>>12)) + toHex(0x80+(c>>6&0x3F)) + toHex(0x80+(c&0x3F)) ; else es += toHex(0xF0+(c>>18)) + toHex(0x80+(c>>12&0x3F)) + toHex(0x80+(c>>6&0x3F)) + toHex(0x80+(c&0x3F)) ; } return es ; } } </script><script type="text/javascript"> !function (t, n) { n = n || "ntm"; window["ntm_" + t] = n, window[n] = window[n] || [], window[n].push({"ntm.start": +new Date}); t = document.getElementsByTagName("script")[0], n = document.createElement("script"); n.async = !0, n.src = "https://ntm.pstatic.net/scripts/ntm_ec8638b0efc1.js", t.parentNode.insertBefore(n, t); }("ec8638b0efc1", "ntm"); </script><script type="text/javascript"> function lcs_do(optional_etc) { var pv_params = optional_etc || {}; var nlogParameter = { "event": "pageviewTrigger", "ssc": pv_params.ssc || undefined, "br_theme": pv_params.br_theme || undefined, "nlogEvt": {} }; delete pv_params.ssc; delete pv_params.br_theme; for (var key in pv_params) { var pv_param = pv_params[key]; nlogParameter.nlogEvt[key] = pv_param; } if (nlogParameter.nlogEvt.pid) { nlogParameter.nlogEvt.page_id = nlogParameter.nlogEvt.pid; delete nlogParameter.nlogEvt.pid; } if (nlogParameter.nlogEvt.sti) { nlogParameter.nlogEvt.page_sti = nlogParameter.nlogEvt.sti; delete nlogParameter.nlogEvt.sti; } var useLocInfo = false; if (useLocInfo) nlogParameter.nlogEvt.lils = {"state": "h"}; if (!nlogParameter.nlogEvt.page_id) nlogParameter.nlogEvt.page_id = window.g_puid; if (!nlogParameter.ssc) nlogParameter.ssc = window.g_ssc; if (!nlogParameter.br_theme && window.naver && window.naver.search && window.naver.search.csdark === 1) nlogParameter.br_theme = "dark"; if (!nlogParameter.nlogEvt.page_qy) nlogParameter.nlogEvt.page_qy = window.g_query; window.ntm.push(nlogParameter); } </script><script> (function () { function report_nx_lcs_call(nx_lcs_method) { var key = { "id": "250123-mobile-pageview-nx-lcs-method", "code": "defense-interface", "area": "nx_lcs" }; var information = { "method": nx_lcs_method }; try { window.naver.common.meerkat.logger.invalidateLogger.invalidate(key, information); } catch (_) { } } nx_lcs = { "set_direct_lp": function () { report_nx_lcs_call("set_direct_lp"); }, "send": function (etc) { report_nx_lcs_call("send"); }, "set_send_bpl": function (val) { report_nx_lcs_call("set_send_bpl"); }, "send_lp": function (etc) { report_nx_lcs_call("send_lp"); } }; })(); </script><script> document.documentElement.setAttribute('data-useragent', window.navigator.userAgent.toLowerCase()) ; document.documentElement.setAttribute('data-platform', window.navigator.platform.toLowerCase()) ; try { document.execCommand('BackgroundImageCache', false, true); } catch(e) {} naver = window.naver || {}; naver.search = naver.search || {}; </script><script> var g_D = 0 ; naver = window.naver || {}; naver.search = naver.search || {}; naver.search.nscs = 0; if (window.matchMedia && naver.search.nscs) { var media = window.matchMedia('(prefers-color-scheme: dark)'); function onMediaChange(e) { naver.search.csdark = e.matches ? 1 : 0; } naver.search.csdark = media.matches ? 1 : 0; if (media.addEventListener) media.addEventListener('change', onMediaChange); else if (media.addListener) media.addListener(onMediaChange); } else { naver.search.csdark = 0; } naver.search.https = window.location.protocol == "https:"; naver.search.meta_referrer = 1; naver.search.ext = naver.search.ext || {}; naver.search.map_api = { v3 : "https://ssl.pstatic.net/sstatic.map/openapi/maps3.js" }; var nx_mobile_agent = 1 ; var headerfooter_query = "중앙공원 롯데캐슬" ; var headerfooter_query_encoded = "%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" ; var headerfooter_time_year = 2025 ; var headerfooter_time_month = 6 ; var headerfooter_time_day = 2 ; var headerfooter_time_hour = 0 ; var headerfooter_time_minute = 55 ; var headerfooter_time_second = 50 ; var headerfooter_time_wday = 1 ; var headerfooter_time_year_s = "2025" ; var headerfooter_time_month_s = "06" ; var headerfooter_time_day_s = "02" ; var headerfooter_time_hour_s = "00" ; var headerfooter_time_minute_s = "55" ; var headerfooter_time_second_s = "50" ; var headerfooter_time_wday_s = "1" ; var g_ssc = "tab.m_web.all" ; var g_query = "중앙공원 롯데캐슬" ; var g_puid = "jvxGtlqosKwssU3QWnKssssstFo-120319" ; var g_suid = "rmd8hW6gtrv/G9PdY9itdg==" ; var g_tab = "m_web" ; var g_stab = "m_web" ; var g_crt = naver.search.abt_param; </script><script> function urlencode (q) { return encodeURIComponent(q).replace(/%20/g, "+").replace(/[!~'()]/g, function(c) { return '%' + c.charCodeAt(0).toString(16).toUpperCase() ; }); } function urldecode (q) { return decodeURIComponent(q.replace(/\+/g, " ")); } function urlexpand (url) { var href = document.location.href ; if (url == "") return href ; if (url.match(/^[-.A-Za-z]+:/)) return url ; if (url.charAt(0) == '#') return href.split("#")[0] + url ; if (url.charAt(0) == '?') return href.split("?")[0] + url ; if (url.charAt(0) == '/') return href.replace(/([^:\/])\/.*$/, "$1") + url ; return href.substring(0, href.lastIndexOf("/")+1) + url ; } function cpip (isBlockInfoRequired) { var ret; var evt, sx = sy = px = py = vw = vh = cn = -1; try { evt = window.event; } catch (e) {} try { sx = evt.clientX - document.body.clientLeft, sy = evt.clientY - document.body.clientTop; } catch (e) {} try { px = document.body.scrollLeft + (sx < 0 ? 0 : sx), py = (document.documentElement || document.body).scrollTop + (sy < 0 ? 0 : sy); } catch (e) {} try { if (evt.pageX) px = evt.pageX; if (evt.pageY) py = evt.pageY; } catch (e) {} try { vw = window.innerWidth, vh = window.innerHeight; } catch (e) {} try { if (oPageLayoutUI != undefined && oPageLayoutUI.getCurrentIndex() >= 0) { cn = oPageLayoutUI.getCurrentIndex() + 1; } } catch (e) {} ret = "px=" + px + "&py=" + py + "&sx=" + sx + "&sy=" + sy + "&vw=" + vw + "&vh=" + vh; if (cn >= 0) ret = ret + "&cn=" + cn; if (isBlockInfoRequired) { try { ret += window.naver.common.heatmapLog.parse(evt); } catch (e) {} } return ret; } var g_BridgeCommonCRParam = { g_puid : null, g_ssc : null, g_query : null, g_tab : null, g_stab : null, g_suid : null, g_crt : null }; function nxIsHashTable (obj) { if(!obj) return false; if(Array.isArray(obj)) return false; if(obj.constructor != Object) return false; return true; } function nxSetBridgeCommonCRParam (htCommonCRParam) { if (nxIsHashTable(htCommonCRParam)) { g_BridgeCommonCRParam.g_puid = htCommonCRParam.g_puid; g_BridgeCommonCRParam.g_query = htCommonCRParam.g_query; g_BridgeCommonCRParam.g_ssc = htCommonCRParam.g_ssc; g_BridgeCommonCRParam.g_tab = htCommonCRParam.g_tab; g_BridgeCommonCRParam.g_stab = htCommonCRParam.g_stab; g_BridgeCommonCRParam.g_suid = htCommonCRParam.g_suid; g_BridgeCommonCRParam.g_crt = htCommonCRParam.g_crt; } } function nxClearBridgeCommonCRParam () { g_BridgeCommonCRParam = { g_puid : null, g_ssc : null, g_query : null, g_tab : null, g_stab : null, g_suid : null, g_crt : null }; } function nxIsValidateBridgeCommonCRParam () { if (g_BridgeCommonCRParam.g_puid && g_BridgeCommonCRParam.g_suid && g_BridgeCommonCRParam.g_ssc) return true; else return false; } function getCRRanking (p) { if (p.match(/(^|[&?])cr=/)) return 0; if (typeof naver.search.section == 'undefined' || typeof naver.search.section.main == 'undefined') return 0; var cr = 0; var info = naver.search.section.main; try { var y, z; var y = p.split('&'); for (var i = 0; i < y.length; i++) { if (z = y[i].split('=')) { if (z[0] == 'a') { for (var j = 0; j < info.length; j++) { var a = info[j].area.split(/[*.]/)[0]; if (z[1].substr(0, a.length) == a) { cr = j + 1; break; } } break; } } } } catch (e) {} return cr; } function isOutlink (u) { if (!u || u.indexOf("://") < 0) return false; var b = (u.search(/^\w*:\/\/([^:/?]*\.|)*(?!(ad)?cr\.)[^.:/?]+\.+naver\.com(:\d*)?(\/|$)/) < 0);  return b; } function nxGetCommonCRParam (p) { var csdark_param = naver.search.csdark === 1 ? "&br_theme=dark" : ""; if (nxIsValidateBridgeCommonCRParam()) { var param = g_BridgeCommonCRParam; return (p.match(/(^|[&?])p=/) ? '' : "p=" + urlencode(param.g_puid) + '&') + "q=" + urlencode(param.g_query) + "&ie=utf8&rev=1" + (p.match(/(^|[&?])ssc=/) ? '' : "&ssc=" + urlencode(param.g_ssc)) + "&f=" + urlencode(param.g_tab) + "&w=" + urlencode(param.g_stab) + "&s=" + urlencode(param.g_suid) + "&time=" + urlencode((new Date()).getTime()) + param.g_crt + csdark_param; } else { return (p.match(/(^|[&?])p=/) ? '' : "p=" + urlencode(g_puid) + '&') + "q=" + urlencode(g_query) + "&ie=utf8&rev=1" + (p.match(/(^|[&?])ssc=/) ? '' : "&ssc=" + urlencode(g_ssc)) + "&f=" + urlencode(g_tab) + "&w=" + urlencode(g_stab) + "&s=" + urlencode(g_suid) + "&time=" + urlencode((new Date()).getTime()) + g_crt + csdark_param; } } function nxGetCRURL (m, a, b, c, d, e) { var p = "" ; var u ; if (c==undefined && d==undefined && e==undefined) { p = (a==undefined?"":"&"+a) + (b==undefined || a.indexOf("u=")==0 || a.indexOf("&u=") > 0 ?"":"&u="+urlencode(b)) ; u = b ; } else { p = (a==undefined ? "" : "&a="+urlencode(a)) + (b==undefined ? "" : "&r="+urlencode(b)) + (c==undefined ? "" : "&i="+urlencode(c)) + (d==undefined ? "" : "&u="+urlencode(d)) + (e==undefined ? "" : "&"+e) ; u = d ; } if (! p) return null ; if (0 && u && URL) { try { var destURL = new URL(u, window.location.href); if (destURL.host == window.location.host && destURL.pathname == window.location.pathname && false) {  var r = /((?:^|&)u=)((?:(?:[^&]*%2F)?search.naver)?%3F(?:[^%&]|%[^2]|%2[^3])*)/; var pu = p.match(r); pu = pu && pu[2] ? pu[2] : '';  p = p.replace(r, '$1' + pu); } } catch (_) {} } var url ; if (naver.search.https) { url = (naver.search.meta_referrer ? "/p/crd" : "/p/cr") + "/rd"; if (m != 0) m = 1; } else { out = isOutlink(u); url = "http://cr.naver.com" + (g_D && out ? "/nr" : "/rd"); if (m != 0) m = g_D && out ? 2 : 1; } var isBlockInfoRequired = false; try { isBlockInfoRequired = window.naver.common.heatmapLog.check(p); } catch (e) {} url += "?m=" + m + "&" + cpip(isBlockInfoRequired) + "&" + nxGetCommonCRParam(p) + p; return url; } var sendLog = {}; sendLog.get = function(url) { if(!url) return false; try { new Image().src = url; return true; } catch (e) { return false; } }; sendLog.post = function(url, data, recover) { if(!url) return false; try { var isSent = !!(navigator && navigator.sendBeacon && navigator.sendBeacon(url, data)); if(!isSent && recover) { var xhr = new XMLHttpRequest(); xhr.open('POST', url); if(typeof data === 'string') { xhr.setRequestHeader('Content-Type', 'text/plain;charset=UTF-8'); xhr.responseType = 'text'; } else if (data instanceof Blob) { xhr.setRequestHeader('Content-Type', data.type); } xhr.send(data); return true; } return isSent; } catch (e) { return false; } }; function isTargetUserAgent() { try { var isIOS = navigator.userAgent.match(/(?:iphone|ipad|ipod)[^;]*; cpu(?: \S+)? os (\d+_\d+)/i); var currentIOS = isIOS && parseInt(isIOS[1].replace(/_/g, ".")); var isAndroid = /android/i.test(navigator.userAgent); return 12 < currentIOS || isAndroid; } catch (e) { return false; } } function _tCR(a, b, c, d, e) { var l = nxGetCRURL(0, a, b, c, d, e); var cr = getCRRanking(l) ; if (cr > 0) l = l + "&cr=" + cr; try { window.naver.common.clickLog.send(l); } catch (e) {} try { l = l.replace(/&nlog=[^&]*/g, ""); } catch (e) {} if (document.images) (new Image()).src = l; else document.location = l; return false; } function tCR(a, b, c, d, e) { var l = nxGetCRURL(0, a, b, c, d, e) ; var cr = getCRRanking(l) ; if (cr > 0) l = l + "&cr=" + cr ; try { window.naver.common.clickLog.send(l); } catch (e) {} try { l = l.replace(/&nlog=[^&]*/g, ""); } catch (e) {} if (document.images) (new Image()).src = l ; else document.location = l ; return false ; } function gCR (u, a, r, i, e, t) { if (u) u = urlexpand(u) ; var l = nxGetCRURL(1, a, r, i, u, e) ; var o = document.createElement("a") ; var cr = getCRRanking(l) ; if (cr > 0) l = l + "&cr=" + cr ; try { window.naver.common.clickLog.send(l); } catch (e) {} try { l = l.replace(/&nlog=[^&]*/g, ""); } catch (e) {} if (o.click) { if (t) o.target = t; else { if (u) o.href = u ; } o.href = l ; o.style.display = "none" ; document.body.appendChild(o) ; o.click() ; } else document.location = l ; } function goCR (o, p, t) { var p0 = o.getAttribute && o.getAttribute("crp"), u = o.href, u0 = u; if (p0 && u.indexOf(p0)>0 && u.match(/\/(rd|nr)\?/)) { u = u0 = o.getAttribute && o.getAttribute("cru") || u; p = p0; } p0 = p; if (p.indexOf("u=javascript") >= 0 || p.indexOf("u=tel%3A") >= 0) t = true; var n = (o.ownerDocument==document && o.target && o.target!="_self" && o.target!="_parent" && o.target!="_top"); var cr = getCRRanking(p); if (cr > 0) p = p + "&cr=" + cr;  u = nxGetCRURL(t?0:(n?-1:1), p, u); try { window.naver.common.clickLog.send(u); } catch (e) {} try { u = u.replace(/&nlog=[^&]*/g, ""); } catch (e) {} if (u && !u.match(/m=0&/)) { if (o.setAttribute) { if (p0) o.setAttribute("crp", p0); if (u0) o.setAttribute("cru", u0); if (g_D && naver.search.https && naver.search.meta_referrer && o.href && isOutlink(o.href)) o.setAttribute("rel", "noreferrer"); } var isBeaconSent = false; if (naver.search.https && isTargetUserAgent()) isBeaconSent = sendLog.post(u); if(!isBeaconSent) { var a = o.innerHTML; o.href = u; if (o.innerHTML != a) o.innerHTML = a; }  } else if (document.images) (new Image()).src = u; return true; } function goOtherCR (o, p) { return goCR(o, p, false) ; } function goOtherTCR (o, p) { return goCR(o, p, true) ; } function get_form_url (o) { var url = o.getAttribute("action") ; if (url == null) url = "" ; var e, n = 0 ; for (var i=0; i<o.elements.length; i++) { e = o.elements[i] ; if (e.disabled || !e.name) continue ; url += (n++>0?"&":url.indexOf("?")<0?"?":url.indexOf("?")<url.length-1?"&":"") + encodeURIComponent(e.name) + "=" + encodeURIComponent(e.value) ; } return url ; } function formCR (o, area, rank, id) { if (typeof o == 'string') o = document.getElementById(o) ; var target = o.getAttribute("target") ; if (target && target!="_self" && target!="_parent" && target!="_top" || /^post$/i.test(o.getAttribute("method"))) { tCR(area, rank, id) ; o.submit() ; return false ; } var url = get_form_url(o) ; var a = document.createElement("a") ; a.href = url ; var p = area != undefined ? "a=" + area : "" ; if (rank != undefined) p += (p?"&":"") + "r=" + encodeURIComponent(rank) ; if (id != undefined) p += (p?"&":"") + "i=" + encodeURIComponent(id) ; if (url != undefined) p += (p?"&":"") + "u=" + encodeURIComponent(urlexpand(url)) ; goCR(a, p) ; if (navigator.userAgent.indexOf('MSIE') > 0) { a.style.display = 'none' ; o.appendChild(a) ; a.click() ; } else document.location = a.href ; return false ; } function concatenateExtraInfo(extraInfo, base) { if(!extraInfo) return base; var s = []; if(base) s.push(base); for (var key in extraInfo) { if (key === "nlog") { try { var n = urlencode(JSON.stringify(extraInfo["nlog"])); s.push("nlog=" + n); } catch (e) {} } else s.push(key + "=" + urlencode(extraInfo[key])); } return s.join("&"); } function goOtherBCR(element, parameter, extraInfo) { return goOtherCR(element, concatenateExtraInfo(extraInfo, parameter)); } function goOtherBTCR(anchorElement, parameter, extraInfo) { return goOtherTCR(anchorElement, concatenateExtraInfo(extraInfo, parameter)); } function stringBTCR(parameter, extraInfo) { return tCR(concatenateExtraInfo(extraInfo, parameter)); } function bTCR(parameter, extraInfo) { return tCR(parameter.area, parameter.rank, parameter.id, parameter.url, concatenateExtraInfo(extraInfo, parameter.extra)); } function bGCR(parameter, extraInfo) { return gCR(parameter.url, parameter.area, parameter.rank, parameter.id, concatenateExtraInfo(extraInfo, parameter.extra), parameter.target); } </script><script src="https://ssl.pstatic.net/sstatic/fe/spg/nx/nx-m/nx-m-1.1.0.min.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/sfe/common/m-1.14.0.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/au/module/requirejs/require-2.3.5.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/sfe/post-requirejs/mobile/app_230919.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/meerkat/logger/sfe/naver.common.meerkat.logger.sfe_mobile_240613.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/sfe/web-vitals/web-vitals_250521.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/sfe/scrollLog/Controller_250424.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/SPF/log-invoker/click/naver.common.clickLog.nlog_241024.js?o=m.search" crossorigin="anonymous"></script><script src="https://ssl.pstatic.net/sstatic/fe/SPF/log-invoker/heatmap/naver.common.heatmapLog.sfe.mobile_250116.js?o=m.search" crossorigin="anonymous"></script><script> 0 && naver.common.meerkat.logger.observe(); </script><script> (function () { var vm = new nhn.mobile.common.VisibleManager(document, {}); vm.initImageLazyLoad(); nhn.mobile.gv.visibleManager = vm; nhn.mobile.gv.imageLazyLoader = vm.getImageLazyLoader(); window.addEventListener("load", function () { setTimeout(function () { vm.check(); }, 500); }); })(); (function () { nhn.common.load_js(null, function(){ var islOptions = { scroll: { capture: true, "interval": 300, "endDelay": 300 }, logFunction: window.tCR }; nhn.mobile.gv.innerScrollLog = new nhn.mobile.common.InnerScrollLog(document.body, islOptions); }, true, 150); }()); </script><style data-img-lazy="">._vsb_img, ._vsb_img_ready{font-size: 0;min-width: 1px;min-height: 1px;display: inline-block;visibility: hidden;}</style><script> (function() { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/module/geolocation-widget/app-1.5.2.js"; var jsLoadCallback = function() { var require = window.require.config({ "context": "search_common_modules", "paths": { "GeolocationWidget": jsFileURL.replace(/\.js$/, "") } }); require(["GeolocationWidget"], function() { if (naver && naver.search && naver.search.nscs) { naver.map.api.loadDarkCss(); } }); }; naver.common.load_js(null, jsLoadCallback, true); })(); </script><script> (function() { var startApplication = function () { var jsFile = "https://ssl.pstatic.net/sstatic/fe/sfe/keep/Controller_240627.js"; var require = window.require.config({ "context": "search_common_module", "paths": { "Keep": jsFile.replace(/\.js$/, "") } }); define("jquery", [], function () { return jQuery; }); require(["Keep"], function (Controller) { var oSearchKeep = new Controller({ "api": { "token": "https://apis.naver.com/naverSearchFe/naver_keep/naver_keep", "check": "https://apis.naver.com/naverSearchFe/naver_keep/v1_web_keep_check?type=bookmark&key={=url}", "create": "https://apis.naver.com/naverSearchFe/naver_keep/v1_web_bookmark_create", "delete": "https://apis.naver.com/naverSearchFe/naver_keep/v1_web_keep_delete" }, "fanApi": { "check": "https://gw.in.naver.com/delivery/api/v1/subscribes?spaceIds={=spaceIds}", "subscribe": "https://gw.in.naver.com/home/<USER>/v4/spaces/{=spaceId}/subscribes", "unsubscribe": "https://gw.in.naver.com/home/<USER>/v4/spaces/{=spaceId}/unsubscribes" }, "captchaApi": { "image": "https://captcha.nid.naver.com/nhncaptchav4.gif?key={=key}", "voice": "https://soundcaptcha.nid.naver.com/soundCaptcha.wav?key={=key}", "token": "https://gw.in.naver.com/identity-access/api/v1/captcha?type={=type}", "refresh": "https://gw.in.naver.com/identity-access/api/v1/refresh-captcha?key={=key}&type={=type}", "check": "https://gw.in.naver.com/identity-access/api/v1/captcha" }, "loginUrl": "https://nid.naver.com/nidlogin.login?svctype=262144&url={=location}" }); oSearchKeep.on({ "create": function (we) { var crLog = we.data("cr-on"); if (crLog) tCR(crLog); }, "delete": function (we) { var crLog = we.data("cr-off"); if (crLog) tCR(crLog); }, "showLayer": function (we) { var crLog = we.data("cr-on"); if (crLog) tCR(crLog); }, "hideLayer": function (we) { var crLog = we.data("cr-off"); if (crLog) tCR(crLog); }, "fan.subscribe": function (we) { var crLog = we.data("cr-on"); if (crLog) tCR(crLog); }, "fan.unsubscribe": function (we) { var crLog = we.data("cr-off"); if (crLog) tCR(crLog); } }); window.naver.common.gv.searchKeep = oSearchKeep; }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> (function() { var jsUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/autopreview/search-1.9.0.js"; var startApplication = function () { var require = window.require.config({ "context": "search_common_modules", "paths": { "Autopreview": jsUrl.replace(/\.js$/, "") } }); require(["Autopreview"], function() { }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> (function () { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/search_video_player2/cav-2.19.0.js"; var jsLoadCallback = function () { var require = window.require.config({ "context": "search_common_modules", "paths": { "CommonAppViewer": jsFileURL.replace(/\.js$/, "") } }); require(["CommonAppViewer"], function (CommonAppViewer) { new naver.common.CommonAppViewer({ "$base": jQuery(document.body), "selector": "._cav_trigger", "api": "https://apis.naver.com/hubsearch/videohub/ingressControlJsonp?videoIds={=vid}&panelType=m.search.naver.com" }) .on("commonViewerOpen", function (e) { var $item = e.$item; var area = $item.attr("data-cr-area") || ""; var gdid = $item.attr("data-cr-gdid") || ""; var gparam = $item.attr("data-cr-gparam") || ""; var rank = $item.attr("data-cr-rank") || ""; var url = $item.attr("data-cr-url") || ""; if (area) { tCR("a=" + area + "&i=" + gdid + "&r=" + rank + "&g=" + urlencode(gparam) + "&u=" + urlencode(url)); } }) .on("commonViewerEndOpen", function (e) { var $item = e.$item; var area = $item.attr("data-cr-area-end") || $item.attr("data-cr-area") || ""; var gdid = $item.attr("data-cr-gdid") || ""; var gparam = $item.attr("data-cr-gparam") || ""; var rank = $item.attr("data-cr-rank") || ""; var url = $item.attr("data-cr-url") || ""; if (area) { tCR("a=" + area + "&i=" + gdid + "&r=" + rank + "&g=" + urlencode(gparam) + "&u=" + urlencode(url)); } }) .on("commonViewerRedirect", function (e) { var $item = e.$item; var area = $item.attr("data-cr-area-redirect") || $item.attr("data-cr-area") || ""; var gdid = $item.attr("data-cr-gdid") || ""; var gparam = $item.attr("data-cr-gparam") || ""; var rank = $item.attr("data-cr-rank") || ""; var url = $item.attr("data-cr-url") || ""; if (area && url) { gCR(url, area, rank, gdid, "&g=" + urlencode(gparam)); } }); }); }; naver.common.load_js(null, jsLoadCallback, true); })(); </script><script> (function() { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/search_video_player2/m-2.19.0.js"; window.require.config({ "context": "search_common_modules", "paths": { "VideoPlayer2": jsFileURL.replace(/\.js$/, "") } }); })(); </script><script> (function() { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/search_audio_player2/m-2.4.0.js"; window.require.config({ "context": "search_common_modules", "paths": { "AudioPlayer2": jsFileURL.replace(/\.js$/, "") } }); })(); </script><script> (function() { var jsControllerUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/responsiveBlock/mobile/Controller_221103.js"; var startApplication = function() { var require = window.require.config({ "context": "search_common_modules", "paths": { "ResponsiveBlock": jsControllerUrl.replace(/\.js$/, "") } }); define("jquery", [], function() { return jQuery; }); require(["ResponsiveBlock"], function(ResponsiveBlock) { new ResponsiveBlock(); }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> (function () { var jsShortFormViewerUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/shortform-viewer/shortform-viewer.min_241217.js"; var startApplication = function () { var require = window.require.config({ "context": "search_common_modules", "paths": { "shortform-viewer": jsShortFormViewerUrl.replace(/\.js$/, ""), } }); require(["shortform-viewer"], function(ShortFormViewer) { new ShortFormViewer() }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> (function() { var jsControllerUrl = "https://ssl.pstatic.net/tveta/libs/glad/prod/gfp-core.js"; var setHostMeta = function() { window.gladsdk.cmd.push(function() { var isDarkMode = naver.search && naver.search.csdark === 1; window.gladsdk.setHostMeta("theme", isDarkMode ? "dark" : "light"); }); }; var startApplication = function() { var require = window.require.config({ "context": "search_common_modules", "paths": { "glad": jsControllerUrl.replace(/\.js$/, "") } }); require(["glad"], function() { window.gladsdk = window.gladsdk || {"cmd": []}; window.gladsdk.cmd.push(function () { if (window.matchMedia("(prefers-color-scheme: dark)") && window.matchMedia("(prefers-color-scheme: dark)").addEventListener) { window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", function() { setHostMeta(); }); } else { window.matchMedia("(prefers-color-scheme: dark)").addListener(function() { setHostMeta(); }); } }); setHostMeta(); }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> if (window.addEventListener) { window.addEventListener( "pageshow", function (event) { if (event.persisted) { lcs_do({ "pid": "jvxGtlqosKwssU3QWnKssssstFo-120319", "ssc": "tab.m_web.all", "search": { "navigation_type": "back_forward_cache", "not_restored_reasons": null } }); } else { var nlog_search = {}; try { var navigationEntry = window.performance.getEntriesByType("navigation")[0]; var notRestoredReasons = navigationEntry.notRestoredReasons; if (navigationEntry.notRestoredReasons) { notRestoredReasons = notRestoredReasons.reasons.map(function (reason) { return reason.reason; }); } nlog_search = { "navigation_type": navigationEntry.type, "not_restored_reasons": notRestoredReasons }; } catch (error) { } lcs_do({ "pid": "jvxGtlqosKwssU3QWnKssssstFo-120319", "ssc": "tab.m_web.all", "search": nlog_search }); } }, false ); } </script><script> var g_m_ver = "17.4" ; var g_inapp = 0 ; var g_inapp_ver = 0 ; function nx_img_lazyload () { var fn; var scroll_timer; function _get_top (el) { var top = el.offsetTop; var parent = el.offsetParent; if (parent) { top += _get_top(parent); } return top; } function _get_viewport () { var y = window.pageYOffset; var h = window.innerHeight; return { y: y, h: h }; } function img_lazyload_update () { if (scroll_timer) { window.clearTimeout(scroll_timer); } scroll_timer = window.setTimeout(function() { img_lazyload_update_do(); }, 10); } function img_lazyload_update_do () { scroll_timer = null; var viewport = _get_viewport(); var h_buffer = 200; var deferred_imgs = $$('img[data-src]'); for (var i = 0; i < deferred_imgs.length; i++) { var img = deferred_imgs[i]; var top = _get_top(img); var lazy = img.getAttribute('data-lazy-load'); if ((!lazy || top != 0) && top < h_buffer + viewport.y + viewport.h && top > viewport.y - h_buffer) { img.setAttribute('src', img.getAttribute('data-src')); img.removeAttribute('data-src') ; } } } var deferred_imgs = $$('img[data-src]'); for (var i = 0; i < deferred_imgs.length; i++) { var img = deferred_imgs[i]; var top = _get_top(img); var lazy = img.getAttribute('data-lazy-load'); if (!lazy && top == 0) { img.setAttribute('src', img.getAttribute('data-src')); img.removeAttribute('data-src') ; } } fn = $Fn(img_lazyload_update, this); if (nx_mobile_agent == '1' && g_m_ver == '7.0' && g_inapp) { fn.attach(window, 'touchend'); } else { fn.attach(window, 'scroll'); } fn.attach(window, 'resize'); jindo.m.bindRotate($Fn(img_lazyload_update, this).bind()); jindo.m.bindPageshow($Fn(img_lazyload_update, this).bind()); window.setTimeout(function() { img_lazyload_update_do(); }, 10); } var nx_dcll = (function() { var isAttached = 0; return function() { if (!isAttached) { setTimeout(function() { nx_img_lazyload() ; }, 10) ; } isAttached = 1; }; })(); jindo.$Fn(function() { nx_dcll(); }).attach(window, "load"); setTimeout(function() { nx_dcll(); }, 6000); </script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="GeolocationWidget" src="https://ssl.pstatic.net/sstatic/fe/module/geolocation-widget/app-1.5.2.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_module" data-requiremodule="Keep" src="https://ssl.pstatic.net/sstatic/fe/sfe/keep/Controller_240627.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="Autopreview" src="https://ssl.pstatic.net/sstatic/fe/sfe/autopreview/search-1.9.0.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="CommonAppViewer" src="https://ssl.pstatic.net/sstatic/fe/sfe/search_video_player2/cav-2.19.0.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="ResponsiveBlock" src="https://ssl.pstatic.net/sstatic/fe/sfe/responsiveBlock/mobile/Controller_221103.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="shortform-viewer" src="https://ssl.pstatic.net/sstatic/fe/sfe/shortform-viewer/shortform-viewer.min_241217.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="glad" src="https://ssl.pstatic.net/tveta/libs/glad/prod/gfp-core.js"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_tab_module" data-requiremodule="IntentNavigation" src="https://ssl.pstatic.net/sstatic/fe/sfe/intentNavigation/m/IntentNavigation_231017.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_au_search_bar" data-requiremodule="Search" src="https://ssl.pstatic.net/sstatic/fe/sfe/search-bar/m/search-3.36.3.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_fe_search_option" data-requiremodule="Controller" src="https://ssl.pstatic.net/sstatic/fe/sfe/searchOption/Controller_230920.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_fe_web_commonportal" data-requiremodule="Controller" src="https://ssl.pstatic.net/sstatic/fe/sfe/commonPortal/m_240116.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="layer-bridge" data-requiremodule="layer-bridge" src="https://ssl.pstatic.net/sstatic/fe/sfe/layer-bridge/LayerBridge.min_250123.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="footer" data-requiremodule="footer" src="https://ssl.pstatic.net/sstatic/fe/sfe/footer/app-1.0.1.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="CrossBlock" src="https://ssl.pstatic.net/sstatic/fe/sfe/cross-block/m-1.2.0.js?o=m.search" crossorigin="anonymous"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="splugin" src="https://ssl.pstatic.net/spi/js/release/ko_KR/splugin.m.js?v=1457327"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="search_common_modules" data-requiremodule="like-it" src="https://ssl.pstatic.net/sstatic/au/m/_likeIt/nhn.mobile.common.likeIt_170309.js?o=m.search" crossorigin="anonymous"></script><script type="module" src="https://ssl.pstatic.net/tveta/libs/glad/prod/3.1.0/gfp-display-sdk.js" async="" charset="utf-8"></script><link rel="stylesheet" type="text/css" href="https://ssl.pstatic.net/sstatic/search/mobile/css/my_lct_s_230921n.css"><link id="naver-splugin-css" rel="stylesheet" type="text/css" href="https://ssl.pstatic.net/spi/css/20250211/spi_layer.css"><script type="text/javascript" src="https://ssl.pstatic.net/melona/libs/gfp-nac-module/synchronizer.js" async="" charset="utf-8"></script></head><body class="s api_animation type_floating tabsch tabsch_total lnb_intent"> <div id="u_skip" class="u_skip"><a href="#ct">본문 바로가기</a></div><header><div class="header _header"><div id="_sch" class="sch"> <section class="_section"> <div class="search_wrap _sch_w"> <h1 class="logo_naver"> <a href="https://m.naver.com/" class="logo_link" onclick="return goOtherCR(this, 'a=gnb.logo&amp;r=&amp;i=&amp;u='+urlencode(this.href));"><i class="spnew2 ico_logo_n">Naver</i></a> <a href="#" class="logo_link prev_link" id="nx_close_ac_kh"><i class="spnew2 ico_logo_prev">이전페이지</i></a> </h1> <form id="nx_search_form" name="search" role="search" method="get" action="?" onsubmit="return nx_form_submit(this)"> <div class="search_input_wrap _sch_inpw"> <div class="search_input_box _sch_inpw_in"> <div class="search_input_img _sch_thumb" style="display:none"></div> <div class="search_input_inner"> <input type="hidden" name="sm" value="mtb_hty.top"> <input type="hidden" name="where" value="m"> <input type="hidden" name="ssc" value="tab.m.all"> <input type="hidden" name="oquery" value="중앙공원 롯데캐슬"> <input type="hidden" name="tqi" value="jvxGtlqosKwssU3QWnKssssstFo-120319">  <input id="nx_query" type="search" name="query" title="검색어를 입력해주세요." value="중앙공원 롯데캐슬" placeholder="검색어를 입력해주세요." maxlength="255" class="search_input" autocorrect="off" autocapitalize="off" autocomplete="off"> <button id="nx_input_clear" type="button" class="btn_delete" onclick="return tCR('a=sch.kwdx&amp;r=1');" style="display: block;"><i class="spnew2 ico_delete">입력 내용 삭제</i></button> </div> </div> <div class="search_btn_box"> <button type="button" class="btn_voice" data-ios-scheme="naversearchapp" data-ios-query="search?qmenu=voicerecg&amp;version=26" data-ios-install="393499958" data-ios-universal-fullurl="https://naverapp.m.naver.com/?urlScheme=naversearchapp%3A%2F%2Fsearch%3Fqmenu%3Dvoicerecg%26version%3D26" data-android-scheme="naversearchapp" data-android-query="search?qmenu=voicerecg&amp;version=26" data-android-package="com.nhn.android.search" onclick="tCR('a=rcs.voice&amp;r=&amp;i=0&amp;u=');nhn.mobile.launchApp(this);return false;"><i class="spnew2 ico_voice">음성검색</i></button> <button type="submit" class="btn_search"><i class="spnew2 ico_search">검색</i></button> </div> </div> <input type="hidden" name="ackey" value="378psuin"></form> </div> </section> <div id="ac-layer-root" class="u_atcp_wrap"><div id="sb-kh" class="u_sggt_wrap2" style="display:none">
  <div id="sb-kh-wrapper" class="sggt_fixer">
    <div class="sggt_header">
      <h2 class="tit"><span class="tit_inner">최근 검색어</span></h2>
      <div class="option">
        <span class="opt_del"><a id="sb-kh-clear" href="#">전체삭제</a></span>
      </div>
    </div>
    <div class="sggt_container">
      <ul id="sb-kh-list" class="kwd_lst"></ul>
      <div id="sb-kh-off" class="kwd_info kwd_off" style="display:none">검색어 저장 기능이 꺼져 있습니다. <br><span class="kwd_dsc">설정이 초기화 된다면 <a href="https://help.naver.com/alias/search/word/word_49.naver" class="kwd_help" onclick="goOtherCR(this, 'a=sxt.help&amp;r=&amp;i=&amp;u=' + urlencode(this.href));">도움말</a>을 확인해 주세요.</span>
      </div>
      <div id="sb-kh-nothing" class="kwd_info kwd_none" style="display:none">최근 검색어 내역이 없습니다. <br><span class="kwd_dsc">설정이 초기화 된다면 <a href="https://help.naver.com/alias/search/word/word_49.naver" class="kwd_help" onclick="goOtherCR(this, 'a=sxt.help&amp;r=&amp;i=&amp;u=' + urlencode(this.href));">도움말</a>을 확인해 주세요.</span>
      </div>
    </div>
    <div id="sb-kh-footer" class="sggt_footer">
      <span class="side_opt_area">
        <span class="opt_option"><a id="sb-kh-unuse" href="#">자동저장 끄기</a></span>
        <span class="opt_help"><a href="https://help.naver.com/alias/search/word/word_49.naver" onclick="goOtherCR(this, 'a=sxt.help&amp;r=&amp;i=&amp;u=' + urlencode(this.href));">도움말</a>
        </span>
      </span>
      <span class="rside_opt_area"><span class="opt_close"><a id="sb-kh-close" href="#">닫기</a></span></span>
    </div>
  </div>
</div>
<div id="sb-ac" class="u_atcp_area" style="display:none">
  <div id="sb-ac-answer-wrap" class="atcp_crt_w" style="display: none;"></div>
  <ul id="sb-ac-recomm-wrap" title="자동완성" class="u_atcp u_atcp_at" style="display: none;"></ul>
  <div id="sb-ac-alert" class="u_atcp_alert" style="display:none"></div>
  <div id="sb-ac-context" class="u_atcp_plus">
    <div class="switch">
      <input type="checkbox" id="sb-ac-context-label" aria-label="컨텍스트 자동완성">
      <label for="sb-ac-context-label" aria-hidden="true"><span></span></label>
    </div>
    <div id="sb-ac-context-layer" class="layer_plus" style="display:none">
      <strong class="tit">컨텍스트 자동완성</strong>
      <p class="dsc _off_dsc"><em class="txt">동일한 시간대・연령대・남녀별</em> 사용자 그룹의 관심사에 <br>맞춰 자동완성을 제공합니다. <a href="https://help.naver.com/alias/search/word/word_16.naver" class="link" onclick="goOtherCR(this, 'a=sug.cxlink');">자세히 보기</a></p>
      <div class="_on_dsc" style="display:none">
        <p class="dsc">ON/OFF설정은 해당기기(브라우저)에 저장됩니다.</p>
        <div class="btn_area">
          <a href="https://help.naver.com/alias/search/word/word_16.naver" class="link" onclick="goOtherCR(this, 'a=sug.cxlink');">자세히 보기</a>
        </div>
      </div>
      <div class="btn_area">
        <a href="https://nid.naver.com/nidlogin.login?svctype=262144&amp;url=https%3A%2F%2Fm.search.naver.com%2Fsearch.naver%3Fnso%3D%26page%3D10%26query%3D%25EC%25A4%2591%25EC%2595%2599%25EA%25B3%25B5%25EC%259B%2590%2B%25EB%25A1%25AF%25EB%258D%25B0%25EC%25BA%2590%25EC%258A%25AC%26sm%3Dmtb_pge%26start%3D121%26where%3Dm_web" class="btn btn_login _btn_login" onclick="goOtherCR(this, 'a=sug.cxlogin');"><i class="imgsvg ico_login"></i>로그인</a>
      </div>
      <button type="button" class="btn_close _btn_close" onclick="tCR('a=sug.cxhide')"><i class="imgsvg ico_close">컨텍스트 자동완성 레이어 닫기</i></button>
    </div>
    <div class="dsc_plus">
      <a href="https://help.naver.com/alias/search/word/word_16.naver" class="dsc_area" onclick="goOtherCR(this, 'a=sug.cxhelp');"><span class="dsc">관심사를 반영한 컨텍스트 자동완성</span><span class="ico_help"><i class="imgsvg">도움말</i></span></a>
    </div>
  </div>
  <div id="sb-ac-footer" class="sggt_footer">
    <div class="side_opt_area">
      <span class="opt_option"><a id="sb-ac-toggle" href="#">자동완성 끄기</a></span>
      <span class="opt_help"><a href="https://help.naver.com/alias/search/word/word_17.naver" onclick="goOtherCR(this, 'a=sug.help');">도움말</a></span>
      <span class="opt_help"><a href="https://help.naver.com/alias/search/word/word_18.naver" class="report" onclick="goOtherCR(this, 'a=sug.report');">신고</a></span>
    </div>
    <span class="rside_opt_area">
      <span class="opt_close"><a id="sb-ac-close" href="#">닫기</a></span>
    </span>
  </div>
</div>
</div> </div><script>var nx_usain_beacon=function(){var n=0,t=[],s={send:function(){if(n>0)return;n=1,setTimeout(function(){var e,n,s,o,a,r,c,i="",i=function(){function e(e){return e?e-window.performance.timing.navigationStart:0}if(typeof window.performance=="undefined")return"";if(typeof window.performance.timing=="undefined")return"";if(typeof window.performance.navigation=="undefined")return"";if(typeof window.g_puid=="undefined")return"";if(typeof window.g_ssc=="undefined")return"";var t=window.performance.timing,n=window.performance.navigation;return"navt="+[e(t.navigationStart),e(t.unloadEventStart),e(t.unloadEventEnd),e(t.redirectStart),e(t.redirectEnd),e(t.fetchStart),e(t.domainLookupStart),e(t.domainLookupEnd),e(t.connectStart),e(t.connectEnd),e(t.requestStart),e(t.responseStart),e(t.responseEnd),e(t.domLoading),e(t.domInteractive),e(t.domContentLoadedEventStart),e(t.domContentLoadedEventEnd),e(t.domComplete),e(t.loadEventStart),e(t.loadEventEnd),n.type,n.redirectCount].join(":")+"&page_id="+encodeURIComponent(window.g_puid)+"&ssc="+encodeURIComponent(window.g_ssc)}();document.location.protocol==="https:"?t.push("_ssl"):t.push("_nossl"),e=eg.agent(),(e.os.name==="android"||e.os.name==="ios")&&(n="",a=e.os.version.split("."),r="_"+e.os.name.slice(0,3),c=/NAVER/.test(navigator.userAgent)||window.g_inapp&&window.g_inapp===1?"_app":"_web",a.length>=1&&(n=a[0]),n&&t.push(r+n+c)),s=[],i&&s.push(i),s.push("tags="+t.join(":")),o=new Image,o.src="https://er.search.naver.com/er?v=2&"+s.join("&"),o.onload=function(){o.onload=null}},250)},add_tag:function(e){t.push(e)}};return s}(jQuery)</script></div><div class="sch_tab_wrap _sch_tab_wrap"> <div class="nav_intent _sch_tab type_t2 has_shadow" id="_sch_tab" style="transition: -webkit-transform 0.001s;"> <div class="api_flicking_wrap" role="tablist" data-log-innerscroll="a=tab.tfk"> <div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*N.jmp&amp;r=1&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_prev">전체</i></a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_news.all&amp;where=m_news&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*n.jmp&amp;r=2&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_news"></i>뉴스</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_blog.all&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*b.jmp&amp;r=3&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_blog"></i>블로그</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_image.all&amp;where=m_image&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*i.jmp&amp;r=4&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_image"></i>이미지</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_cafe.all&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*c.jmp&amp;r=5&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_cafe"></i>카페</a></div><div class="flick_bx" role="presentation"><a role="tab" href="https://m.map.naver.com/search.nhn?query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*M.jmp&amp;r=6&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false">지도</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_kin.all&amp;where=m_kin&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*k.jmp&amp;r=7&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_kin"></i>지식iN</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_influencer.chl&amp;where=m_influencer&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*F.jmp&amp;r=8&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_inf"></i>인플루언서</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_clip.all&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*C.jmp&amp;r=9&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_clip"></i>클립</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_video.all&amp;where=m_video&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*v.jmp&amp;r=10&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_video"></i>동영상</a></div><div class="flick_bx" role="presentation"><a role="tab" href="https://msearch.shopping.naver.com/search/all?query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*S.jmp&amp;r=11&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false">쇼핑</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_surf.tab1&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*f.jmp&amp;r=12&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_feed"></i>서치피드</a></div><div class="flick_bx" role="presentation"><a role="tab" href="?ssc=tab.m_shortents.all&amp;sm=mtb_jum&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*d.jmp&amp;r=13&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false"><i class="spnew2 ico_nav_shortents"></i>숏텐츠</a></div><div class="flick_bx" role="presentation"><a role="tab" href="https://dict.naver.com/dict.search?query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;from=tsearch" onclick="return goOtherCR(this,'a=tab*L.jmp&amp;r=14&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false">어학사전</a></div><div class="flick_bx" role="presentation"><a role="tab" href="https://msearch.shopping.naver.com/book/search?query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*B.jmp&amp;r=15&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false">도서</a></div><div class="flick_bx" role="presentation"><a role="tab" href="https://terms.naver.com/search.naver?query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC" onclick="return goOtherCR(this,'a=tab*K.jmp&amp;r=16&amp;i=&amp;u='+urlencode(this.href));" class="tab" aria-selected="false">지식백과</a></div><div class="flick_bx" role="presentation"><a role="tab" href="#" class="tab option _option_on_off" onclick="return tCR('a=tab.optn');" aria-pressed="false">검색옵션</a></div></div> </div> </div><script> (function() { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/intentNavigation/m/IntentNavigation_231017.js"; var startApplication = function() { var require = window.require.config({ "context": "search_tab_module", "paths": { "IntentNavigation": jsFileURL.replace(/\.js$/, "") } }); define("jquery", [], function() { return jQuery; }); require(["IntentNavigation"], function(IntentNavigation) { var $root = jQuery("#_sch_tab"); new IntentNavigation($root); }); }; var requirejs = naver.common.gv.REQUIRE_JS; naver.common.load_js(window.require ? null : requirejs, startApplication, true, 150); })(); </script><script> function nx_form_submit(f) { if (typeof naver.search.option !== 'undefined') naver.search.option.append_params(f, ['qdt', 'qvt']); return true ; } </script><script> function __atcmpSubmitBefore(f) { nx_form_submit(f); } (function () { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/search-bar/m/search-3.36.3.js"; var jsLoadCallback = function () { var require = window.require.config({ context: "_au_search_bar", paths: { "Search": jsFileURL.replace(/\.js$/, "") } }); require(["Search"], function(Search) { new Search({ APIURL : "//mac.search.naver.com/mobile/ac", ABTAPIURL: "//mac.search.naver.com/mobile/abt.naver", isLogin : false, rcode: "", beforeSubmit: function () { __atcmpSubmitBefore(document.search); } }); }); }; nhn.common.load_js(null, jsLoadCallback, true, 150); })(); </script><script> naver.search.section = { "main": [{"area": "web_lis*w", "rank": 1}], "find_section_list": function(column, area) { if (!this[column]) return []; return this[column].filter(function(a) { return a.area.indexOf(area) === 0; }); } }; </script><script> var nx_cr_area_info = [{"n": "web_lis", "r": 1}]; </script></header> <hr class="skip"><div id="container" class="_slog_visible_page" data-slog-p="jvxGtlqosKwssU3QWnKssssstFo-120319" data-slog-sscode="tab.m_web.all" data-slog-pg="jvxGtlqosKwssU3QWnKssssstFo-120319" data-slog-pn="1" data-slog-url="https://s.search.naver.com/n/scrolllog/v2?u=https%3A%2F%2Fm.search.naver.com%2Fsearch.naver%3Fnso%3D%26page%3D10%26query%3D%25EC%25A4%2591%25EC%2595%2599%25EA%25B3%25B5%25EC%259B%2590%2B%25EB%25A1%25AF%25EB%258D%25B0%25EC%25BA%2590%25EC%258A%25AC%26sm%3Dmtb_pge%26start%3D121%26where%3Dm_web&amp;q=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sscode=tab.m_web.all&amp;pg=jvxGtlqosKwssU3QWnKssssstFo-120319" data-slog-visible="true"><div id="ct"><script> naver.search.option = {} ; naver.search.option.params = {} ; naver.search.option.append_params = (function () { return function (f, exclude) { if (exclude == undefined) exclude = []; for (var k in naver.search.option.params) { if (exclude.indexOf(k) >= 0) continue; var v = naver.search.option.params[k] ; var input = document.createElement("input"); input.type = "hidden"; input.name = k ; input.value = v ; f.appendChild(input); } return true ; } ; })() ; naver.search.option.get_params = (function () { return function () { var option_params = "" ; for (var k in naver.search.option.params) { var v = naver.search.option.params[k]; option_params = option_params.concat("&" + k + "=" + urlencode(v)); } return option_params ; } ; })() ; </script><div id="snb" class="_slog_visible" data-slog-container="opt" data-slog-visible="false"> <form id="nx_option_form" name="nx_option_form" action="?"> <input type="hidden" id="ssc" name="ssc" value="tab.m.all"> <input type="hidden" id="where" name="where" value="m"> <input type="hidden" id="query" name="query" value="중앙공원 롯데캐슬"> <input type="hidden" id="sm" name="sm" value="mtb_opt"> </form> <div role="listbox" class="mod_group_option_sort _search_option_detail_wrap" style="display:none"> <button onclick="return tCR('a=opt.fold');" type="button" class="spnew_bf bt_close _search_option_close_btn">검색옵션 닫기</button> <ul class="lst_option"> <li class="bx lineup"> <div class="bx_inner"> <strong class="tit">정렬</strong> <div role="tablist" class="option"> <a onclick="return goOtherCR(this, 'a=opt.sim&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=so%3Ar&amp;nso_open=1" role="tab" class="txt" aria-selected="true">관련도순</a><a onclick="return goOtherCR(this, 'a=opt.new&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=so%3Add&amp;nso_open=1" role="tab" class="txt" aria-selected="false">최신순</a> </div> </div> </li> <li class="bx term"> <div class="bx_inner"> <strong class="tit">기간</strong> <div role="tablist" class="option"> <a onclick="return goOtherCR(this, 'a=opt.all&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3Aall&amp;nso_open=1" role="tab" class="txt" aria-selected="true">전체</a><a onclick="return goOtherCR(this, 'a=opt.1hr&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A1h&amp;nso_open=1" role="tab" class="txt" aria-selected="false">1시간</a><a onclick="return goOtherCR(this, 'a=opt.1day&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A1d&amp;nso_open=1" role="tab" class="txt" aria-selected="false">1일</a><a onclick="return goOtherCR(this, 'a=opt.1wk&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A1w&amp;nso_open=1" role="tab" class="txt" aria-selected="false">1주</a><a onclick="return goOtherCR(this, 'a=opt.1mon&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A1m&amp;nso_open=1" role="tab" class="txt" aria-selected="false">1개월</a><a onclick="return goOtherCR(this, 'a=opt.3mon&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A3m&amp;nso_open=1" role="tab" class="txt" aria-selected="false">3개월</a><a onclick="return goOtherCR(this, 'a=opt.6mon&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A6m&amp;nso_open=1" role="tab" class="txt" aria-selected="false">6개월</a><a onclick="return goOtherCR(this, 'a=opt.1yr&amp;r=&amp;i=&amp;u='+urlencode(this.href));" href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso=p%3A1y&amp;nso_open=1" role="tab" class="txt" aria-selected="false">1년</a> <a onclick="tCR('a=opt.cal');" href="#" role="tab" class="txt txt_option _calendar_select_trigger" data-select-trigger="" aria-selected="false">직접입력<i class="spnew ico_check">옵션<em class="open">펼치기</em><em class="close">접기</em></i></a> </div> <div class="mod_select_option type_calendar _calendar_select_layer" data-select-layer="" style="display: none;"> <div role="tablist" class="set_calendar"> <span class="set"> <span class="blind">기간 설정시작</span> <a onclick="return tCR('a=opt.str');" href="#" role="tab" class="spnew_bf ico_calendar _start_trigger" aria-selected="true">2025.06.02.</a> </span> <span class="set etc"> <span class="ico_since">~</span> </span> <span class="set"> <span class="blind">기간 설정끝</span> <a onclick="tCR('a=opt.fin');" href="#" role="tab" class="spnew_bf ico_calendar _end_trigger">2025.06.02.</a> </span> </div> <div class="select_wrap _root"> <div class="group_select _list_root"> <strong class="blind">년(Year)</strong> <div class="select_cont"> <div class="select_area _scroll_wrapper"> <div> <ul role="tablist" class="lst_item _ul"><li class="item _li" data-value="1990" aria-selected="false"><a href="#" class="link">1990</a></li><li class="item _li" data-value="1991" aria-selected="false"><a href="#" class="link">1991</a></li><li class="item _li" data-value="1992" aria-selected="false"><a href="#" class="link">1992</a></li><li class="item _li" data-value="1993" aria-selected="false"><a href="#" class="link">1993</a></li><li class="item _li" data-value="1994" aria-selected="false"><a href="#" class="link">1994</a></li><li class="item _li" data-value="1995" aria-selected="false"><a href="#" class="link">1995</a></li><li class="item _li" data-value="1996" aria-selected="false"><a href="#" class="link">1996</a></li><li class="item _li" data-value="1997" aria-selected="false"><a href="#" class="link">1997</a></li><li class="item _li" data-value="1998" aria-selected="false"><a href="#" class="link">1998</a></li><li class="item _li" data-value="1999" aria-selected="false"><a href="#" class="link">1999</a></li><li class="item _li" data-value="2000" aria-selected="false"><a href="#" class="link">2000</a></li><li class="item _li" data-value="2001" aria-selected="false"><a href="#" class="link">2001</a></li><li class="item _li" data-value="2002" aria-selected="false"><a href="#" class="link">2002</a></li><li class="item _li" data-value="2003" aria-selected="false"><a href="#" class="link">2003</a></li><li class="item _li" data-value="2004" aria-selected="false"><a href="#" class="link">2004</a></li><li class="item _li" data-value="2005" aria-selected="false"><a href="#" class="link">2005</a></li><li class="item _li" data-value="2006" aria-selected="false"><a href="#" class="link">2006</a></li><li class="item _li" data-value="2007" aria-selected="false"><a href="#" class="link">2007</a></li><li class="item _li" data-value="2008" aria-selected="false"><a href="#" class="link">2008</a></li><li class="item _li" data-value="2009" aria-selected="false"><a href="#" class="link">2009</a></li><li class="item _li" data-value="2010" aria-selected="false"><a href="#" class="link">2010</a></li><li class="item _li" data-value="2011" aria-selected="false"><a href="#" class="link">2011</a></li><li class="item _li" data-value="2012" aria-selected="false"><a href="#" class="link">2012</a></li><li class="item _li" data-value="2013" aria-selected="false"><a href="#" class="link">2013</a></li><li class="item _li" data-value="2014" aria-selected="false"><a href="#" class="link">2014</a></li><li class="item _li" data-value="2015" aria-selected="false"><a href="#" class="link">2015</a></li><li class="item _li" data-value="2016" aria-selected="false"><a href="#" class="link">2016</a></li><li class="item _li" data-value="2017" aria-selected="false"><a href="#" class="link">2017</a></li><li class="item _li" data-value="2018" aria-selected="false"><a href="#" class="link">2018</a></li><li class="item _li" data-value="2019" aria-selected="false"><a href="#" class="link">2019</a></li><li class="item _li" data-value="2020" aria-selected="false"><a href="#" class="link">2020</a></li><li class="item _li" data-value="2021" aria-selected="false"><a href="#" class="link">2021</a></li><li class="item _li" data-value="2022" aria-selected="false"><a href="#" class="link">2022</a></li><li class="item _li" data-value="2023" aria-selected="false"><a href="#" class="link">2023</a></li><li class="item _li" data-value="2024" aria-selected="false"><a href="#" class="link">2024</a></li><li class="item _li" data-value="2025" aria-selected="true"><a href="#" class="link">2025</a></li></ul> </div> </div> </div> </div> <div class="group_select _list_root"> <strong class="blind">월(Month)</strong> <div class="select_cont"> <div class="select_area _scroll_wrapper"> <div> <ul role="tablist" class="lst_item _ul"><li class="item _li" data-value="1" aria-selected="false"><a href="#" class="link">1</a></li><li class="item _li" data-value="2" aria-selected="false"><a href="#" class="link">2</a></li><li class="item _li" data-value="3" aria-selected="false"><a href="#" class="link">3</a></li><li class="item _li" data-value="4" aria-selected="false"><a href="#" class="link">4</a></li><li class="item _li" data-value="5" aria-selected="false"><a href="#" class="link">5</a></li><li class="item _li" data-value="6" aria-selected="true"><a href="#" class="link">6</a></li></ul> </div> </div> </div> </div> <div class="group_select _list_root"> <strong class="blind">일(Day)</strong> <div class="select_cont"> <div class="select_area _scroll_wrapper"> <div> <ul role="tablist" class="lst_item _ul"><li class="item _li" data-value="1" aria-selected="false"><a href="#" class="link">1</a></li><li class="item _li" data-value="2" aria-selected="true"><a href="#" class="link">2</a></li></ul> </div> </div> </div> </div> </div> <div class="btn_area"> <button data-cr-area="opt.set" type="button" class="btn_apply _apply_btn">적용</button> </div> </div> </div> </li> <li class="bx clear"> <div class="bx_inner"> <div class="option"> <a href="?ssc=tab.m.all&amp;where=m&amp;sm=mtb_opt&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;nso_open=1" onclick="return goOtherCR(this,'a=opt.reset&amp;r=&amp;i=&amp;u='+urlencode(this.href));" class="btn_clear spnew_bf" role="button">옵션 초기화</a> <a href="https://m.help.naver.com/support/alias/search/integration_m/integration_m107.naver" onclick="return goOtherCR(this,'a=opt.help&amp;r=&amp;i=&amp;u='+urlencode(this.href));" class="link_help">검색옵션 가이드<i class="spnew api_ico_help"></i></a> </div> </div> </li> </ul> </div> </div><script> naver.search.option.append_params(jQuery("#nx_option_form")[0]); </script> <script> (function() { var jsFileURL = "https://ssl.pstatic.net/sstatic/fe/sfe/searchOption/Controller_230920.js"; var startApplication = function() { var require = window.require.config({ "context": "_fe_search_option", "paths": { /* Controller.js의 경로 지정. */ "Controller": jsFileURL.replace(/\.js$/, "") } }); /* module define은 require의 context별로 호출해야함. */ define("jquery", [], function() { return jQuery; }); require(["Controller"], function(Controller) { var oSearchOption = new Controller({ "$form": jQuery("#nx_option_form") }); oSearchOption.on({ "selectCalendar": function(e) { var param = e.param; var cr = { "area": e.$item.data("cr-area") }; var date_from = e.startDate; var date_to = e.endDate; var from = date_from.year + "." + date_from.month + "." + date_from.date; from = from.replace(/\./g, ''); var to = date_to.year + "." + date_to.month + "." + date_to.date; to = to.replace(/\./g, ''); var opt_p = "p:from" + (from < to ? from : to) + "to" + (from < to ? to : from); var params = naver.search.option.params; var nso = params["nso"]; if (nso) { var arr = nso.split(","); var isPeriodExist = false; arr.forEach(function(element, index, arr){ if (element.indexOf("p:") == 0) { arr[index] = opt_p; isPeriodExist = true; } }); if (!isPeriodExist){ arr.push(opt_p); } nso = arr.join(","); } else { nso = opt_p; } params["nso"] = nso; param = ""; for (var k in params) { var v = params[k]; param += "&" + k + "=" + urlencode(v); } if (param[0] === "&") { param = param.substring(1); } /* formCR 클릭로그 호출 */ this.submit(param, cr); } }); }); }; var requirejs = naver.common.gv.REQUIRE_JS; naver.common.load_js(window.require ? null : requirejs, startApplication, true, 150); })(); </script><section class="sc sp_ntotal sc_ht _prs_web_lis _fe_root_web_lis"><div class="api_subject_bx type_noline"><ul class="lst_total"><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="http://www.siminsori.com/news/articleView.html?idxno=261927" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=1&amp;i=a00000fa_491f4775aa8ded1231510983%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="http://www.siminsori.com/news/articleView.html?idxno=261927" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=1&amp;i=a00000fa_491f4775aa8ded1231510983%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=http%3A%2F%2Fwww.siminsori.com%2Fimage%2Flogo%2Ffavicon_20171127025117.ico&amp;type=f30_30_png_expire24"></span><span class="name">시민의소리</span><span class="url">www.siminsori.com<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="http://www.siminsori.com/news/articleView.html?idxno=261927" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=1&amp;i=a00000fa_491f4775aa8ded1231510983%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 5일 견본주택 오픈!</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="http://www.siminsori.com/news/articleView.html?idxno=261927" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=1&amp;i=a00000fa_491f4775aa8ded1231510983%5EfaG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=http%3A%2F%2Fwww.siminsori.com%2Fnews%2Fthumbnail%2F202404%2F261927_226882_4046_v150.jpg&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="http://www.siminsori.com/news/articleView.html?idxno=261927" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=1&amp;i=a00000fa_491f4775aa8ded1231510983%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.05.</span></span>
<mark>롯데</mark>건설이 5일 광주시 최대 민간<mark>공원</mark> 특례사업으로 선보이는 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 견본주택을 오픈한다. 본격적으로 분양에 나설 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’는 광주 서구 금호동 일대에 위치하며, 지하 3층~지상 28층 총 39개 동 전용 84~233㎡ 총 2,772가구 규모로 총 3개 블록으로 나뉘어 △1BL(929가구) △2-1BL(915가구...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://smallblog.tistory.com/62" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=2&amp;i=a00000fa_551cf46d24436136a9614bbd%5EaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://smallblog.tistory.com/62" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=2&amp;i=a00000fa_551cf46d24436136a9614bbd%5EaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><i class="spnew api_ico_web_default"></i></span><span class="name">집에 대한 생각</span><span class="url">smallblog.tistory.com</span></a></div><div class="api_txt_lines total_tit"><a href="https://smallblog.tistory.com/62" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=2&amp;i=a00000fa_551cf46d24436136a9614bbd%5EaG&amp;u=&quot;+urlencode(this.href))">광주 <mark>중앙공원 롯데캐슬</mark> 시그니처 139B</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://smallblog.tistory.com/62" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=2&amp;i=a00000fa_551cf46d24436136a9614bbd%5EaG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fimg1.daumcdn.net%2Fthumb%2FR800x0%2F%3Ffname%3Dhttps%253A%252F%252Fblog.kakaocdn.net%252Fdn%252FczyaBc%252FbtsJecb1HTY%252FBdz1keK4TbkiRdY7KvjWg1%252Fimg.png&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="https://smallblog.tistory.com/62" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=2&amp;i=a00000fa_551cf46d24436136a9614bbd%5EaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.08.22.</span></span>
오늘 소개 해 드릴 내용은 광주 <mark>중앙공원 롯데캐슬</mark> 시그니처 아파트 139B타입 견본주택 입니다. 139A 타입과는 다른 구조이기 때문에 나름 인기 있는 타입입니다. 139제곱미터는 약 42평에 해당하고 2-1블럭은 20세대, 2-2블럭에는 129세대를 분양하고 있습니다. 아쉽지만 1블럭에는 해당 평형은 없습니다. 입구에 들어서서 좌측으로는 넉넉한 신발...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://m.edaily.co.kr/news/read?newsId=01872886638851856" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=3&amp;i=a00000fa_dd6f7452eca4fe94d61f38e6%5Efa&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://m.edaily.co.kr/news/read?newsId=01872886638851856" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=3&amp;i=a00000fa_dd6f7452eca4fe94d61f38e6%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fwww.edaily.co.kr%2Fresources%2Fimages%2Ficon%2Ffavicon.ico&amp;type=f30_30_png_expire24"></span><span class="name">이데일리</span><span class="url">www.edaily.co.kr<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="https://m.edaily.co.kr/news/read?newsId=01872886638851856" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=3&amp;i=a00000fa_dd6f7452eca4fe94d61f38e6%5Efa&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 이달 분양</a></div></div><div class="total_group"><div class="total_dsc_wrap"><a href="https://m.edaily.co.kr/news/read?newsId=01872886638851856" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=3&amp;i=a00000fa_dd6f7452eca4fe94d61f38e6%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.01.</span></span>
<mark>롯데</mark>건설은 광주 최대 민간<mark>공원</mark> 특례사업인 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’를 이달 분양할 예정이다.<mark>중앙공원 롯데캐슬</mark> 시그니처 (사진=<mark>롯데</mark>건설)‘<mark>중앙공원 롯데캐슬</mark> 시그니처’는 광주 서구 금호동 일대에 위치하며, 지하 3층~지상 28층 총 39개 동 전용 84~233㎡ 총 ...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://www.newsquest.co.kr/news/articleView.html?idxno=221603" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=4&amp;i=a00000fa_dd666f83f1a4f99fca08a5c9%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://www.newsquest.co.kr/news/articleView.html?idxno=221603" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=4&amp;i=a00000fa_dd666f83f1a4f99fca08a5c9%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fcdn.newsquest.co.kr%2Fimage%2Flogo%2Ffavicon.png&amp;type=f30_30_png_expire24"></span><span class="name">뉴스퀘스트</span><span class="url">www.newsquest.co.kr<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="https://www.newsquest.co.kr/news/articleView.html?idxno=221603" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=4&amp;i=a00000fa_dd666f83f1a4f99fca08a5c9%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, 광주 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 17일 1순위 청약</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://www.newsquest.co.kr/news/articleView.html?idxno=221603" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=4&amp;i=a00000fa_dd666f83f1a4f99fca08a5c9%5EfaG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fcdn.newsquest.co.kr%2Fnews%2Fphoto%2F202404%2F221603_116001_1723.jpg&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="https://www.newsquest.co.kr/news/articleView.html?idxno=221603" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=4&amp;i=a00000fa_dd666f83f1a4f99fca08a5c9%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.15.</span></span>
<mark>롯데</mark>건설이 오는 17일 광주광역시 최대 민간<mark>공원</mark> 특례사업으로 선보이는 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’의 1순위 청약접수를 진행한다고 15일 밝혔다.이 단지는 광주 서구 금호동 일대에 위치하며 지하 3층~지상 28층 총 39개동, 전용면적 84~233㎡, 총 2772세대로 이 중 2364세대를 일반 분양한다.총 3개 블록으로...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://kimjh71.tistory.com/16476308" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=5&amp;i=a00000fa_c80ee1632de3dfce16a882c4%5EfG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://kimjh71.tistory.com/16476308" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=5&amp;i=a00000fa_c80ee1632de3dfce16a882c4%5EfG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Ft1.daumcdn.net%2Ftistory_admin%2Ffavicon%2Ftistory_favicon_32x32.ico&amp;type=f30_30_png_expire24"></span><span class="url">kimjh71.tistory.com</span></a></div><div class="api_txt_lines total_tit"><a href="https://kimjh71.tistory.com/16476308" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=5&amp;i=a00000fa_c80ee1632de3dfce16a882c4%5EfG&amp;u=&quot;+urlencode(this.href))">광주 <mark>중앙공원 롯데캐슬</mark> 시그니처가 제공하는 힐링라이프 프리미엄 /광주 <mark>중앙공원</mark>과 풍암 호수<mark>공원</mark> 그리고 염주 종합운동장 알아보기</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://kimjh71.tistory.com/16476308" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=5&amp;i=a00000fa_c80ee1632de3dfce16a882c4%5EfG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fblog.kakaocdn.net%2Fdn%2FcesShR%2FbtsJpphOP0R%2FF1dmAsI7hn1L61AEvrNYI1%2Fimg.png&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="https://kimjh71.tistory.com/16476308" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=5&amp;i=a00000fa_c80ee1632de3dfce16a882c4%5EfG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.09.03.</span></span>
안녕하세요 이웃님 바람 인사드립니다. 9월 첫주 화요일의 문이 열렸습니다. 9월 한달 건강하시고 좋은일만 가득하세요 광주 서구에서 분양 하고 있는 광주 <mark>중앙공원 롯데캐슬</mark> 시그니처 아파트에 대해 안내를 드렸습니다. 광주 <mark>중앙공원 롯데캐슬</mark> 시그니처 아파트는 민간<mark>공원</mark> 특례사업으로 조성되는 아파트로 74만평의 <mark>중앙공원</mark> 안에 세워지는 아파트 입니다. 광주 최대...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://www.smarttoday.co.kr/news/articleView.html?idxno=78527" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=6&amp;i=a00000fa_a466b8d6a0e490d96672adea%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=78527" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=6&amp;i=a00000fa_a466b8d6a0e490d96672adea%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fcdn.smarttoday.co.kr%2Fimage%2Flogo%2Ffavicon.ico&amp;type=f30_30_png_expire24"></span><span class="name">스마트투데이</span><span class="url">www.smarttoday.co.kr<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=78527" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=6&amp;i=a00000fa_a466b8d6a0e490d96672adea%5EfaG&amp;u=&quot;+urlencode(this.href))">‘<mark>중앙공원 롯데캐슬</mark> 시그니처’, 국가도시<mark>공원</mark> 최대 수혜 기대</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=78527" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=6&amp;i=a00000fa_a466b8d6a0e490d96672adea%5EfaG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fcdn.smarttoday.co.kr%2Fnews%2Fthumbnail%2F202504%2F78527_71756_390_v150.jpg&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=78527" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=6&amp;i=a00000fa_a466b8d6a0e490d96672adea%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2025.04.18.</span></span>
|스마트투데이=김윤진 기자| 최근 광주광역시 최대 민간<mark>공원</mark> ‘<mark>중앙</mark>근린<mark>공원</mark>’이 전국 최초 국가도시<mark>공원</mark>으로 지정될 가능성이 높아지며 기대를 모으고 있다.이를 통해 광주시의 위상이 한층 더 높아지는 동시에 지역 최대 민간<mark>공원</mark> 특례사업으로 추진 중인 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’의 주거 가치</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://ondal1102.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84%EC%A0%84%EC%84%B8-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EC%95%88%EB%82%B4" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=7&amp;i=a00000fa_f1fd20436660bcbcd347c11c%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://ondal1102.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84%EC%A0%84%EC%84%B8-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EC%95%88%EB%82%B4" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=7&amp;i=a00000fa_f1fd20436660bcbcd347c11c%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Ft1.daumcdn.net%2Ftistory_admin%2Ffavicon%2Ftistory_favicon_32x32.ico&amp;type=f30_30_png_expire24"></span><span class="name">이야기공장</span><span class="url">ondal1102.tistory.com<i class="bar">›</i>중앙공원-롯데캐슬-10년전세-모델하우스-안내</span></a></div><div class="api_txt_lines total_tit"><a href="https://ondal1102.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84%EC%A0%84%EC%84%B8-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EC%95%88%EB%82%B4" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=7&amp;i=a00000fa_f1fd20436660bcbcd347c11c%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>중앙공원 롯데캐슬</mark> 10년전세 모델하우스 안내</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://ondal1102.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84%EC%A0%84%EC%84%B8-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EC%95%88%EB%82%B4" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=7&amp;i=a00000fa_f1fd20436660bcbcd347c11c%5EfaG&amp;u=&quot;+urlencode(this.href))"><img width="104" height="104" alt="" class="img api_get" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fimg1.daumcdn.net%2Fthumb%2FR800x0%2F%3Ffname%3Dhttps%253A%252F%252Fblog.kakaocdn.net%252Fdn%252FcmFKCg%252FbtsKpgROpGs%252FnBkLya2B8W9K48zFbKaJoK%252Fimg.webp&amp;type=fff208_208_ar"></a></div><div class="total_dsc_wrap"><a href="https://ondal1102.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84%EC%A0%84%EC%84%B8-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EC%95%88%EB%82%B4" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=7&amp;i=a00000fa_f1fd20436660bcbcd347c11c%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.10.30.</span></span>
<mark>중앙공원 롯데캐슬</mark> 10년전세 모델하우스 안내 <mark>중앙공원 롯데캐슬</mark> 시그니처에서 10년전세 입주자를 모집한다고 합니다.10년이라는 장기 전세로 공급되는 임대동의 위치는 2-1블럭이며, 84타입 (34평) 408세대가 공급된다고 하는데요. 현재 모델하우스 내부 관람을 진행중에 있으며, 11월 청약오픈과 함께 본격적인 공급일정에 들어갈 예정입니다. 청약 관련...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://m.edaily.co.kr/news/read?newsId=01886006638853168" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=8&amp;i=a00000fa_a6d1058f8e8de7a1cb938fa3%5Efa&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://m.edaily.co.kr/news/read?newsId=01886006638853168" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=8&amp;i=a00000fa_a6d1058f8e8de7a1cb938fa3%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Fwww.edaily.co.kr%2Fresources%2Fimages%2Ficon%2Ffavicon.ico&amp;type=f30_30_png_expire24"></span><span class="name">이데일리</span><span class="url">www.edaily.co.kr<i class="bar">›</i>News</span></a></div><div class="api_txt_lines total_tit"><a href="https://m.edaily.co.kr/news/read?newsId=01886006638853168" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=8&amp;i=a00000fa_a6d1058f8e8de7a1cb938fa3%5Efa&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, 광주 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 견본주택 개관</a></div></div><div class="total_group"><div class="total_dsc_wrap"><a href="https://m.edaily.co.kr/news/read?newsId=01886006638853168" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=8&amp;i=a00000fa_a6d1058f8e8de7a1cb938fa3%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.05.</span></span>
<mark>롯데</mark>건설이 5일 광주 최대 민간<mark>공원</mark> 특례사업으로 선보이는 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 견본주택을 열고 본격적인 분양에 나선다고 밝혔다.‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 1BL 투시도 (사진=<mark>롯데</mark>건설)광주 서구 금호동 일대에 위치한 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’는 지하 ...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://urimming.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84-%EC%9E%84%EB%8C%80-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EB%B6%84%EC%96%91%EA%B0%80-%EC%95%88%EB%82%B4" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=9&amp;i=a00000fa_1e5f098afa3f23c9dab11683%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://urimming.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84-%EC%9E%84%EB%8C%80-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EB%B6%84%EC%96%91%EA%B0%80-%EC%95%88%EB%82%B4" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=9&amp;i=a00000fa_1e5f098afa3f23c9dab11683%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img width="16" height="16" alt="" src="https://search.pstatic.net/sunny?src=https%3A%2F%2Ft1.daumcdn.net%2Ftistory_admin%2Ffavicon%2Ftistory_favicon_32x32.ico&amp;type=f30_30_png_expire24"></span><span class="name">행복한우리집</span><span class="url">urimming.tistory.com<i class="bar">›</i>중앙공원-롯데캐슬-10년-임대-모델하우스-분양가-안내</span></a></div><div class="api_txt_lines total_tit"><a href="https://urimming.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84-%EC%9E%84%EB%8C%80-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EB%B6%84%EC%96%91%EA%B0%80-%EC%95%88%EB%82%B4" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=9&amp;i=a00000fa_1e5f098afa3f23c9dab11683%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>중앙공원 롯데캐슬</mark> 10년 임대 모델하우스 분양가 안내</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://urimming.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84-%EC%9E%84%EB%8C%80-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EB%B6%84%EC%96%91%EA%B0%80-%EC%95%88%EB%82%B4" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=9&amp;i=a00000fa_1e5f098afa3f23c9dab11683%5EfaG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fimg1.daumcdn.net%2Fthumb%2FR800x0%2F%3Ffname%3Dhttps%253A%252F%252Fblog.kakaocdn.net%252Fdn%252Fb6XiSM%252FbtsKgNbqXOq%252FsQ5Pj7KgID5yzBzEHBJcPK%252Fimg.png&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="https://urimming.tistory.com/entry/%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90-%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC-10%EB%85%84-%EC%9E%84%EB%8C%80-%EB%AA%A8%EB%8D%B8%ED%95%98%EC%9A%B0%EC%8A%A4-%EB%B6%84%EC%96%91%EA%B0%80-%EC%95%88%EB%82%B4" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=9&amp;i=a00000fa_1e5f098afa3f23c9dab11683%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.10.24.</span></span>
<mark>중앙공원 롯데캐슬</mark> 10년 임대 모델하우스 분양가 안내 안녕하세요. 오늘은 광주의 대장아파트라 불리고 있는 <mark>중앙공원 롯데캐슬</mark> 10년 임대 공급소식 알려드리고자 찾아왔어요. 민간<mark>공원</mark> 특례사업으로 진행되며 광주 서구 랜드마크에 들어서는 처음이자 마지막 대단지 아파트이기 때문에 많은 이들의 이목이 집중되고 있습니다. <mark>중앙공원 롯데캐슬</mark> 34평 임대! 5억 5천...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="http://m.kwangju.co.kr/article.php?aid=1740291900780364004" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=10&amp;i=a00000fa_16d2a8c27e9431c078f1686d%5EfG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="http://m.kwangju.co.kr/article.php?aid=1740291900780364004" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=10&amp;i=a00000fa_16d2a8c27e9431c078f1686d%5EfG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=http%3A%2F%2Fwww.kwangju.co.kr%2Ffavicon.ico&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="url">www.kwangju.co.kr<i class="bar">›</i>article</span></a></div><div class="api_txt_lines total_tit"><a href="http://m.kwangju.co.kr/article.php?aid=1740291900780364004" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=10&amp;i=a00000fa_16d2a8c27e9431c078f1686d%5EfG&amp;u=&quot;+urlencode(this.href))">‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ … 멤버십 라운지 문 열다 - 광주일보</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="http://m.kwangju.co.kr/article.php?aid=1740291900780364004" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=10&amp;i=a00000fa_16d2a8c27e9431c078f1686d%5EfG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/sunny?src=http%3A%2F%2Fwww.kwangju.co.kr%2Fupimages%2Fgisaimg%2F202502%2F23_780363.jpg&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="http://m.kwangju.co.kr/article.php?aid=1740291900780364004" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=10&amp;i=a00000fa_16d2a8c27e9431c078f1686d%5EfG&amp;u=&quot;+urlencode(this.href))">광주시 최대 민간<mark>공원</mark> 특례사업 아파트인 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’가 지역 최초로 계약자들에게 최상급 편의·휴식공간을 제공하기 위한 ‘멤버십 라운지’를 오픈해 화제가 되고 있다.지난 21일부터 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 모델하우스에 문을 연 ‘멤버십 라운지’는 계약자들이 분양 ...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://www.smarttoday.co.kr/news/articleView.html?idxno=46449" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=11&amp;i=a00000fa_0bac458ba5208872c0979897%5Efa&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=46449" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=11&amp;i=a00000fa_0bac458ba5208872c0979897%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fcdn.smarttoday.co.kr%2Fimage%2Flogo%2Ffavicon.ico&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="name">스마트투데이</span><span class="url">www.smarttoday.co.kr<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=46449" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=11&amp;i=a00000fa_0bac458ba5208872c0979897%5Efa&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, 광주 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’ 4월 분양</a></div></div><div class="total_group"><div class="total_dsc_wrap"><a href="https://www.smarttoday.co.kr/news/articleView.html?idxno=46449" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=11&amp;i=a00000fa_0bac458ba5208872c0979897%5Efa&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.03.26.</span></span>
<mark>롯데</mark>건설은 광주광역시 최대 민간<mark>공원</mark> 특례사업인 ‘<mark>중앙공원 롯데캐슬</mark> 시그니처’를 4월 중에 분양할 계획이라고 26일 밝혔다. 광주 서구 금호동 일대에 선보이는 <mark>중앙공원 롯데캐슬</mark> 시그니처는 지하 3층~지상 28층 총 39개 동, 전용 84~233㎡ 총 2772가구 규모다. 총 3개 블록으</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://wonderfe.tistory.com/69" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=12&amp;i=a00000fa_e8dc0c627557f2894eb1d5b8%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://wonderfe.tistory.com/69" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=12&amp;i=a00000fa_e8dc0c627557f2894eb1d5b8%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Ft1.daumcdn.net%2Ftistory_admin%2Ffavicon%2Ftistory_favicon_32x32.ico&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="name">원더풀라이프 분양정보</span><span class="url">wonderfe.tistory.com</span></a></div><div class="api_txt_lines total_tit"><a href="https://wonderfe.tistory.com/69" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=12&amp;i=a00000fa_e8dc0c627557f2894eb1d5b8%5EfaG&amp;u=&quot;+urlencode(this.href))">광주 <mark>중앙공원 롯데캐슬</mark> 10년 전세 확정분양가 안내</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://wonderfe.tistory.com/69" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=12&amp;i=a00000fa_e8dc0c627557f2894eb1d5b8%5EfaG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fimg1.daumcdn.net%2Fthumb%2FR800x0%2F%3Ffname%3Dhttps%253A%252F%252Fblog.kakaocdn.net%252Fdn%252FRgBVT%252FbtsKsetFpHI%252FgsNKNqlEXFTlboIlg6yMvk%252Fimg.jpg&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="https://wonderfe.tistory.com/69" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=12&amp;i=a00000fa_e8dc0c627557f2894eb1d5b8%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.11.01.</span></span>
광주 <mark>중앙공원 롯데캐슬</mark> 시그니처 '관심고객 등록하기'네이버 폼 설문에 바로 참여해 보세요.form.naver.com 안녕하세요. 강력한 대출 규제가 시행됨에 따라 부동산 시장의 상승 폭이 점점 축소되어 가는 모습입니다. 그러나 각 지역 내 대장주 아파트들은 신고가를 기록하는 등 여전히 높은 인기를 누리고 있는데요. 오늘은 관련해서 광주 <mark>중앙공원 롯데</mark>캐...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://www.youthdaily.co.kr/mobile/article.html?no=149370" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=13&amp;i=a00000fa_29517cae60bcfba3e929a8f5%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://www.youthdaily.co.kr/mobile/article.html?no=149370" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=13&amp;i=a00000fa_29517cae60bcfba3e929a8f5%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fwww.youthdaily.co.kr%2Fdata%2Fskin%2Flayout%2F1%2Fm13%2Fimages%2Ffavicon.ico&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="name">청년일보</span><span class="url">www.youthdaily.co.kr<i class="bar">›</i>news</span></a></div><div class="api_txt_lines total_tit"><a href="https://www.youthdaily.co.kr/mobile/article.html?no=149370" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=13&amp;i=a00000fa_29517cae60bcfba3e929a8f5%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, 광주 '<mark>중앙공원 롯데캐슬</mark> 시그니처' 분양</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://www.youthdaily.co.kr/mobile/article.html?no=149370" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=13&amp;i=a00000fa_29517cae60bcfba3e929a8f5%5EfaG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fwww.youthdaily.co.kr%2Fdata%2Fphotos%2F20240414%2Fart_17122755605366_d5dba5.jpg&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="https://www.youthdaily.co.kr/mobile/article.html?no=149370" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=13&amp;i=a00000fa_29517cae60bcfba3e929a8f5%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.05.</span></span>
<mark>롯데</mark>건설이 광주광역시 최대 민간<mark>공원</mark> 특례사업으로 선보이는 '<mark>중앙공원 롯데캐슬</mark> 시그니처' 견본주택을 열고 본격적인 분양에 나선다고 5일 밝혔다. 광주 서구 금호동 일대에 위치한 '<mark>중앙공원 롯데캐슬</mark> 시그니처'는 지하 3층~지상 28층 총 39개동, 전용면적 84~233㎡, 총 2천772세대로 구성되며, 이 중 2천364세대를 일반 분양한다. 총</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://m.blog.naver.com/worship0327/223865831796" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=14&amp;i=90000003_00000000000000341F718174%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://m.blog.naver.com/worship0327/223865831796" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=14&amp;i=90000003_00000000000000341F718174%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fssl.pstatic.net%2Ft.static.blog%2Fpcfe%2Fstatic%2Fpc_favicon.ico%3F2&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="name">발품이</span><span class="url">blog.naver.com<i class="bar">›</i>worship0327</span></a></div><div class="api_txt_lines total_tit"><a href="https://m.blog.naver.com/worship0327/223865831796" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=14&amp;i=90000003_00000000000000341F718174%5EfaG&amp;u=&quot;+urlencode(this.href))">광주 <mark>중앙공원 롯데캐슬</mark> 모델하우스 공급조건</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://m.blog.naver.com/worship0327/223865831796" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=14&amp;i=90000003_00000000000000341F718174%5EfaG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/common?src=http%3A%2F%2Fblogfiles.naver.net%2FMjAyNTA1MTRfMjQ1%2FMDAxNzQ3MjI3MjUwNjY3.rzaop91U8jw7G8EYNwsprxBFIz1RLkDytFlujLnO-S0g.eu-Tmj9FspOiMFv1V2BEWLmbZmuvOSAeeDTv56MSFX0g.PNG%2F0-1.png&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="https://m.blog.naver.com/worship0327/223865831796" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=14&amp;i=90000003_00000000000000341F718174%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">3주 전</span></span>
여러 보고와 통계에 따르면 도심 속 <mark>공원</mark>과 인접한 단지들 위주로 집값이 상승하는 경향을 보였지요. 이번에 들어서는 광주 <mark>중앙공원 롯데캐슬</mark> 모델하우스는 시내를 대표하는 공세권 입지라서 많은 관심을 모았습니다. 이 단지는 세 블록에 걸쳐 지어지는 대단위 규모였습니다. 주소지로 서구...</a></div></div></div></li><li class="bx"><div class="total_wrap"><div class="total_tit_group"><div class="total_source"><div class="api_save_group _keep_wrap"><a href="#" role="button" class="btn_save _keep_trigger" data-url="https://www.g-enews.com/article/Real-Estate/2024/04/202404081510438755bf11c0d58c_1" onclick="tCR(&quot;a=web_lis*w.kep&amp;r=&amp;i=&amp;u=javascript&quot;)"><i class="spnew ico_save">문서 저장하기</i></a><div class="api_ly_save _keep_save_layer"><a href="#" role="button" class="spnew_af item item_save _keep_save" data-cr-on="a=web_lis*w.keepon" data-cr-off="a=web_lis*w.keepoff">Keep에 저장</a>
<a href="https://m.keep.naver.com/" class="spnew_af item item_quick" onclick="goOtherCR(this,&quot;a=web_lis*w.kep&amp;r=15&amp;i=a00000fa_cda7b27515954529fcc8a61f%5EfaG&amp;u=&quot;+urlencode(this.href))">Keep 바로가기</a></div></div><a href="https://www.g-enews.com/article/Real-Estate/2024/04/202404081510438755bf11c0d58c_1" class="link_source elss" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=15&amp;i=a00000fa_cda7b27515954529fcc8a61f%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="thumb"><img data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fwww.g-enews.com%2Ffavicon.ico&amp;type=f30_30_png_expire24" width="16" height="16" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></span><span class="name">글로벌이코노믹</span><span class="url">www.g-enews.com<i class="bar">›</i>분양</span></a></div><div class="api_txt_lines total_tit"><a href="https://www.g-enews.com/article/Real-Estate/2024/04/202404081510438755bf11c0d58c_1" class="link_tit" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=15&amp;i=a00000fa_cda7b27515954529fcc8a61f%5EfaG&amp;u=&quot;+urlencode(this.href))"><mark>롯데</mark>건설, '<mark>중앙공원 롯데캐슬</mark> 시그니처' 견본주택 2만명 몰려</a></div></div><div class="total_group"><div class="thumb_single _parent"><a href="https://www.g-enews.com/article/Real-Estate/2024/04/202404081510438755bf11c0d58c_1" class="thumb_link" onclick="goOtherCR(this,&quot;a=web_lis*w.image&amp;r=15&amp;i=a00000fa_cda7b27515954529fcc8a61f%5EfaG&amp;u=&quot;+urlencode(this.href))"><img data-img-alias="hide" data-img-eparam="._parent" data-lazysrc="https://search.pstatic.net/sunny?src=https%3A%2F%2Fnimage.g-enews.com%2Fphpwas%2Frestmb_allidxmake.php%3Fidx%3D5%26simg%3D2024040815121004499bf11c0d58c2114012247.jpg&amp;type=fff208_208_ar" width="104" height="104" alt="" class="img api_get" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" data-obstatus="ob-once"></a></div><div class="total_dsc_wrap"><a href="https://www.g-enews.com/article/Real-Estate/2024/04/202404081510438755bf11c0d58c_1" class="api_txt_lines total_dsc" onclick="goOtherCR(this,&quot;a=web_lis*w.link&amp;r=15&amp;i=a00000fa_cda7b27515954529fcc8a61f%5EfaG&amp;u=&quot;+urlencode(this.href))"><span class="dsc_sub"><span class="sub_txt">2024.04.08.</span></span>
<mark>롯데</mark>건설이 지난 5일 개관한 광주광역시 최대 민간<mark>공원</mark> 특례사업 아파트 '<mark>중앙공원 롯데캐슬</mark> 시그니처' 견본주택에 주말 3일간 총 2만여 명의 방문객이 몰렸다.8일 <mark>롯데</mark>건설에 따르면 지난 주말 내내 '<mark>중앙공원 롯데캐슬</mark> 시그니처' 견본주택은 수많은 방문객들로 인산인해를 이뤘다. 광주시 중심입</a></div></div></div></li></ul></div></section><div class="sp_maximum _maximum" style="display:none;" id="web_max_display" name="web_max_display"><p class="dsc">네이버 검색은 최상의 검색결과 품질을 위해 600건 이상의 웹문서결과를 제공하지 않습니다. 원하시는 검색결과를
찾으시려면 <em class="point">더 구체적인 검색어</em>를 입력해 보세요.</p></div><script>(function(){var e=function(){var e=jQuery("._fe_root_web_lis"),t="https://ssl.pstatic.net/sstatic/fe/sfe/commonPortal/m_240116.js",n=window.require.config({context:"_fe_web_commonportal",paths:{Controller:t.replace(/\.js$/,"")}});n(["Controller"],function(t){var n=new t(e);n.on({"click-folder-trigger-button":function(e){e.isOn?tCR(e.$trigger.data("cr-area-off"),"",""):tCR(e.$trigger.data("cr-area-on"),"","")}})})},t=naver.common.gv.REQUIRE_JS;naver.common.load_js(window.require?null:t,e,!0,150)})()</script><div class="sp_page _slog_visible" data-slog-container="pag" data-slog-visible="false"><div class="api_subject_bx"><div class="page_wrap"><button type="button" class="btn_prev" onclick="gCR(&quot;?nso=&amp;page=5&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=106&amp;where=m_web&quot;,&quot;pag*w.pag&quot;,&quot;5&quot;)">
<i class="spnew ico_arr">이전페이지</i></button><div class="list_page"><a href="?nso=&amp;page=6&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=61&amp;where=m_web" onclick="goOtherCR(this,&quot;a=pag.pag&amp;r=6&amp;i=&amp;u=&quot;+urlencode(this.href))" role="button" class="pgn">6</a>
<a href="?nso=&amp;page=7&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=76&amp;where=m_web" onclick="goOtherCR(this,&quot;a=pag.pag&amp;r=7&amp;i=&amp;u=&quot;+urlencode(this.href))" role="button" class="pgn">7</a>
<a href="?nso=&amp;page=8&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=91&amp;where=m_web" onclick="goOtherCR(this,&quot;a=pag.pag&amp;r=8&amp;i=&amp;u=&quot;+urlencode(this.href))" role="button" class="pgn">8</a>
<a href="?nso=&amp;page=9&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=106&amp;where=m_web" onclick="goOtherCR(this,&quot;a=pag.pag&amp;r=9&amp;i=&amp;u=&quot;+urlencode(this.href))" role="button" class="pgn">9</a>
<a href="?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pge&amp;start=121&amp;where=m_web" onclick="goOtherCR(this,&quot;a=pag.pag&amp;r=10&amp;i=&amp;u=&quot;+urlencode(this.href))" role="button" class="pgn now">10</a></div><button type="button" class="btn_next dimmed" aria-disabled="true">
<i class="spnew ico_arr">다음페이지</i></button></div></div></div><div class="sp_nkeyword _slog_visible" id="_related_keywords_aside" data-slog-container="rsk_btm" data-slog-visible="false"> <div class="api_subject_bx"> <div class="bx _rk_hlimit"> <h3 class="stit">연관<span class="etc">검색어</span><a href="https://help.naver.com/alias/search/word/word_1.naver" class="link_help" onclick="return goOtherCR(this, 'a=rsk_btm.help&amp;r=&amp;i=&amp;u='+urlencode(urlexpand(this.href)));"><i class="spnew api_ico_help">도움말</i></a></h3> <div class="keyword _rk_hcheck"><a href="?where=m&amp;ssc=tab.m.all&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%ACll&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*a.list&amp;r=1&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*a:EclZxfWs0MYgAG+4cLOQzqzCbABsAA==" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">중앙공원 롯데캐슬ll</span><span class="clip_right">캐슬ll</span></span></a> <a href="?where=m&amp;ssc=tab.m.all&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC+%EC%8B%9C%EA%B7%B8%EB%8B%88%EC%B2%98&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*q.list&amp;r=2&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*q:EclZxfWs0MYgAG+4cLOQzqzCIADcwvityLKYzA==" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">중앙공원 롯데캐슬 시그니처</span><span class="clip_right">시그니처</span></span></a> <a href="?where=m&amp;ssc=tab.m.all&amp;query=%EA%B4%91%EC%A3%BC+%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*a.list&amp;r=3&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*a:Ea38yCAAEclZxfWs0MYgAG+4cLOQzqzC" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">광주 중앙공원 롯데캐슬</span><span class="clip_right">롯데캐슬</span></span></a> <a href="?where=m&amp;ssc=tab.m.all&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC+%EB%B6%84%EC%96%91%EA%B0%80&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*q.list&amp;r=4&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*q:EclZxfWs0MYgAG+4cLOQzqzCIACEvZHFAKw=" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">중앙공원 롯데캐슬 분양가</span><span class="clip_right">분양가</span></span></a> <a href="?where=m&amp;ssc=tab.m.all&amp;query=%EA%B4%91%EC%A3%BC+%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*q.list&amp;r=5&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*q:Ea38yCAAEclZxfWs0MYgAG+4cLM=" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">광주 중앙공원 롯데</span><span class="clip_right">원 롯데</span></span></a> <a href="?where=m&amp;ssc=tab.m.all&amp;query=%EA%B4%91%EC%A3%BC+%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+1%EC%A7%80%EA%B5%AC&amp;sm=mtb_she&amp;qdt=0" onclick="goOtherCR(this, 'a=rsk_btm*q.list&amp;r=6&amp;i=&amp;u=' + urlencode(urlexpand(this.href)))" class="clip _slog_visible" data-slog-content="rsk_btm*q:Ea38yCAAEclZxfWs0MYgADEAwMlsrQ==" data-slog-visible="false"><i class="spnew ico_kwd_s"></i><span class="clip_wrap"><span class="clip_left" style="right:64px;">광주 중앙공원 1지구</span><span class="clip_right">1지구</span></span></a> </div> </div> <a href="https://m.help.naver.com/support/alias/search/word_m/word_m5.naver" class="ico_report" onclick="return goOtherCR(this, 'a=rsk_btm.report&amp;r=&amp;i=&amp;u='+urlencode(urlexpand(this.href)))">신고</a> <button type="button" class="bt_more _open_btn" onclick="tCR('a=rsk_btm.unfd&amp;r=&amp;i=&amp;m=0&amp;u=javascript');"><i class="spnew ico_more_arr">펼치기</i></button> <button type="button" class="bt_more bt_more_close _close_btn" onclick="tCR('a=rsk_btm.fold&amp;r=&amp;i=&amp;m=0&amp;u=javascript');"><i class="spnew ico_more_arr">접기</i></button> </div> </div> <script type="text/javascript"> (function () { var elRk = document.getElementById("_related_keywords_aside"); var oRk = new nhn.mobile.search.related_search_word.Main(elRk); nhn.common.load_js(null, function(){ oRk.loadPersist(); oRk.initComponents(); }, true, 150); })(); </script> </div> <hr class="skip"><div class="ct_feed_wrap"></div><div class="mod_bridge_layer _sfe_layer_bridge_root" data-lb-render-type="mobile" style="display: none"> <div class="_lb_init_target"> <div class="bridge_header"> <div class="header_wrap _lb_header_root"> <div class="header_area"> <div class="header_box"> <div class="title_wrap"> <a role="button" href="#" class="link_prev _lb_header_back_button"><i class="spnew ico_prev">이전</i></a> </div> </div> </div> </div> </div> <div class="block_container _lb_content_root"> <div class="api_result_wrap _lb_content_loading"> <div class="api_loading"> <div class="api_load_wrap"> <div class="api_load"> <div class="dot dot1"></div> <div class="dot dot2"></div> <div class="dot dot3"></div> <div class="dot dot4"></div> <div class="dot dot5"></div> <div class="dot dot6"></div> </div> </div> <p class="dsc_loading">정보를 가져오는 중입니다.</p> </div> </div> <div class="api_result_wrap _lb_content_error" style="display: none"> <div class="api_error"> <i class="spnew api_ico_error"></i> <strong class="tit_message">죄송합니다. 일시적인 오류 입니다.</strong> <p class="dsc_message">잠시 후 다시 시도해주십시오.</p> <a href="#" role="button" class="btn_retry _lb_content_retry"><i class="spnew api_ico_retry2"></i>재시도</a> </div> </div> </div> </div> <div class="_lb_open_target" style="display: none"></div> <div class="bridge_footer"> <div class="api_footer"> <div class="api_info_service"> <div class="service_area"> <span class="info"><a href="https://policy.naver.com/policy-mobile/term.html" onclick="return goOtherCR(this, 'a=fot.policy&amp;u='+urlencode(this.href));">이용약관</a></span> <span class="info stress"><a href="https://policy.naver.com/policy-mobile/privacy.html" onclick="return goOtherCR(this, 'a=fot.privacy&amp;u='+urlencode(this.href));">개인정보처리방침</a></span> <span class="info"><a href="https://help.naver.com/alias/search/integration/main.naver" onclick="return goOtherCR(this, 'a=fot.searchhelp&amp;u='+urlencode(this.href));">검색 고객센터</a></span> </div> <a href="https://www.navercorp.com/" onclick="return goOtherCR(this, 'a=fot.navercorp&amp;r=&amp;i=&amp;u='+urlencode(this.href));" class="service_logo"><i class="spnew ico_logo">NAVER</i></a> </div> </div> </div> </div><script> (function () { var jsUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/layer-bridge/LayerBridge.min_250123.js"; var startApplication = function () { var require = window.require.config({ "context": "layer-bridge", "paths": { "layer-bridge": jsUrl.replace(/\.js$/, "") } }); define("jquery", [], function () { return jQuery; }); require(["layer-bridge"], function (LayerBridge) { var layerBridge = new LayerBridge({ "root": "._sfe_layer_bridge_root", "initTarget": "._lb_init_target", "openTarget": "._lb_open_target", "header": { "root": "._lb_header_root", "box": "_lb_header_box", "backButton": "._lb_header_back_button" }, "content": { "root": "._lb_content_root", "loading": "._lb_content_loading", "retryButton": "._lb_content_retry", "error": "._lb_content_error", "appendTarget": "._lb_content_append_target" } }); window.naver = window.naver || {}; window.naver.common = window.naver.common || {}; window.naver.common.layerBridge = layerBridge; }); }; naver.common.load_js(null, startApplication); })(); </script></div> <hr class="skip"><div id="container_more"><div id="ct_more"></div><div class="ct_feed_wrap"></div></div><footer> <div class="api_footer"><div class="footer_function"> <div class="footer_tool"> <span class="item"><a href="#" id="btn_font_small" class="link dimmed" role="button" onclick="tCR('a=fot.smalltype');">가<i class="spnew ico_small">폰트작게보기</i></a></span><span class="item"><a href="#" id="btn_font_large" class="link" role="button" onclick="tCR('a=fot.bigtype');">가<i class="spnew ico_big">폰트크게보기</i></a></span> </div> <div class="footer_btn"> <span class="item"><a onclick="goOtherTCR(this, 'a=fot.pplugin'); return false;" href="#" role="button" class="link spnew_bf ico_share u_social naver-splugin" data-style="standard" data-use-short-url="on" data-mail-srv-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.mail&amp;start=121&amp;where=m_web" data-blog-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.blog&amp;start=121&amp;where=m_web" data-cafe-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.cafe&amp;start=121&amp;where=m_web" data-cafe-source-form="2" data-memo-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.memo&amp;start=121&amp;where=m_web" data-calendar-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.cal&amp;start=121&amp;where=m_web" data-bookmark-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=msv_pin.bmk&amp;start=121&amp;where=m_web" data-twitter-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=twt_pin&amp;start=121&amp;where=m_web" data-facebook-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=fbk_pin&amp;start=121&amp;where=m_web" data-band-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=bnd_pin&amp;start=121&amp;where=m_web" data-line-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=lin_pin&amp;start=121&amp;where=m_web" data-kakaotalk-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=kat_pin&amp;start=121&amp;where=m_web" data-mypeople-url="https://m.search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mpl_pin&amp;start=121&amp;where=m_web" data-spi-click-code="SPO.release" splugin-id="2924427455"><span class="blind">공유하기</span></a></span><span class="item"><a href="#" class="link" role="button" onclick="tCR('a=fot.top');" id="a_go_top"><i class="spnew ico_top">맨위로</i></a></span> </div> </div><div class="footer_etc_wrap"> <div class="footer_etc"> <span class="item"><a href="https://nid.naver.com/nidlogin.login?svctype=262144&amp;url=https%3A%2F%2Fm.search.naver.com%2Fsearch.naver%3Fnso%3D%26page%3D10%26query%3D%25EC%25A4%2591%25EC%2595%2599%25EA%25B3%25B5%25EC%259B%2590%2B%25EB%25A1%25AF%25EB%258D%25B0%25EC%25BA%2590%25EC%258A%25AC%26sm%3Dmtb_pge%26start%3D121%26where%3Dm_web" class="link" onclick="return goOtherCR(this, 'a=fot.login&amp;r=&amp;i=&amp;u='+urlencode(this.href));">로그인</a></span> <span class="item"><a href="https://m.naver.com/services.html?f=svc.search" class="link" onclick="return goOtherCR(this, 'a=fot.sitemap&amp;u='+urlencode(this.href));">전체서비스</a></span> <span class="item"><a href="https://search.naver.com/search.naver?nso=&amp;page=10&amp;query=%EC%A4%91%EC%95%99%EA%B3%B5%EC%9B%90+%EB%A1%AF%EB%8D%B0%EC%BA%90%EC%8A%AC&amp;sm=mtb_pcv&amp;start=121&amp;where=web&amp;ssc=tab.web.all" class="link" id="go_to_pc_version" onclick="return goOtherCR(this, 'a=fot.pc&amp;r=&amp;i=&amp;u='+urlencode(this.href));">PC버전</a></span> </div> </div> <div class="api_info_service"> <div class="service_area"> <span class="info"><a href="https://policy.naver.com/policy-mobile/term.html" onclick="return goOtherCR(this, 'a=fot.policy&amp;u='+urlencode(this.href));">이용약관</a></span> <span class="info stress"><a href="https://policy.naver.com/policy-mobile/privacy.html" onclick="return goOtherCR(this, 'a=fot.privacy&amp;u='+urlencode(this.href));">개인정보처리방침</a></span> <span class="info"><a href="https://help.naver.com/alias/search/integration/main.naver" onclick="return goOtherCR(this, 'a=fot.searchhelp&amp;u='+urlencode(this.href));">검색 고객센터</a></span> </div> <a href="https://www.navercorp.com/" class="service_logo" onclick="return goOtherCR(this, 'a=fot.navercorp&amp;r=&amp;i=&amp;u='+urlencode(this.href));"><i class="spnew ico_logo">NAVER</i></a> </div></div> </footer> <script> (function () { var jsUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/footer/app-1.0.1.js"; var startApplication = function () { var require = window.require.config({ "context": "footer", "paths": { "footer": jsUrl.replace(/\.js$/, "") } }); define("jquery", [], function () { return jQuery; }); require(["jquery", "footer"], function ($, Footer) { Footer({ "fontSizeDownBtn": $("#btn_font_small").get(0), "fontSizeUpBtn": $("#btn_font_large").get(0), "gotoTopBtn": $("#a_go_top").get(0), "gotoPcVersion": $("#go_to_pc_version").get(0) }); }); }; naver.common.load_js(null, startApplication, true, 150); })(); </script> <div id="_rs_dimmed_layer" class="api_ly_dimmed type_header"></div> <script> (function () { var startApplication = function() { var $root = jQuery("#container"); var p = urlencode(window.g_puid); var pg = p; var sscode = urlencode(window.g_ssc); var url = "https://s.search.naver.com/n/scrolllog/v2?u=" + urlencode(location.href) + "&q=" + window.headerfooter_query_encoded; var oScrollLog = new window.naver.common.ScrollLog(url, pg); window.naver.common.scrollLog = oScrollLog; oScrollLog.init($root, { "p": p, "sscode": sscode, "pn": 1 }); }; startApplication(); })(); </script><script> (function () { var startApplication = function () { var url = "https://s.search.naver.com/n/responsive/v1?u=" + urlencode(location.href) + "&q=" + window.headerfooter_query_encoded; naver.common.responsiveLog = new window.naver.common.ResponsiveLog(); naver.common.responsiveLog.init(url); }; startApplication(); })(); </script><script> (function() { var jsControllerUrl = "https://ssl.pstatic.net/sstatic/fe/sfe/cross-block/m-1.2.0.js"; var startApplication = function() { var require = window.require.config({ "context": "search_common_modules", "paths": { "CrossBlock": jsControllerUrl.replace(/\.js$/, "") } }); define("jquery", [], function() { return jQuery; }); require(["CrossBlock"], function(CrossBlock) { new CrossBlock(); }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> function splugin_oninitialize(sTargetId) { var elTarget = document.getElementById(sTargetId); var sUrl = $$.getSingle("a._sp_each_url", elTarget).href; var sTitle = $Element($$.getSingle("._sp_each_title", elTarget)).text(); var sSource = $Element($$.getSingle("._sp_each_source", elTarget)); if (sSource) sSource = sSource.text(); return { "url": sUrl, "title": sTitle, "sourceName": sSource }; } var g_nx_splugin; (function () { var jsUrl = "https://ssl.pstatic.net/spi/js/release/ko_KR/splugin.m.js"; var startApplication = function () { var require = window.require.config({ "context": "search_common_modules", "paths": { "splugin": jsUrl.replace(/\.js$/, "") }, "shim": { "splugin": { "exports": "SocialPlugIn_Core" } }, "urlArgs": function(id, url) { if (id !== "splugin") return ""; var args = "v=" + Math.floor(new Date().getTime() / 1200000); return (url.indexOf("?") === -1 ? "?" : "&") + args; } }); require(["splugin"], function (SocialPlugIn_Core) { g_nx_splugin = SocialPlugIn_Core({ "evKey": "search", "serviceName": "검색", "dimmed": "fixed", "darkMode" : false }); }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> var g_nx_likeit_controller; (function () { var jsUrl = "https://ssl.pstatic.net/sstatic/au/m/_likeIt/nhn.mobile.common.likeIt_170309.js"; var startApplication = function () { var require = window.require.config({ "context": "search_common_modules", "paths": { "like-it": jsUrl.replace(/\.js$/, "") }, "shim": { "like-it": { "exports": "nhn.mobile.common.likeIt" } } }); require(["like-it"], function (likeIt) { g_nx_likeit_controller = new likeIt.Controller(); }); }; naver.common.load_js(null, startApplication, true); })(); </script><script> if (window.location.hash) nx_dcll() ; </script><script> if(typeof nx_usain_beacon !== 'undefined') { nx_usain_beacon.add_tag("conn_r_TLSv1.3_r:alpn.h2:wtm") ; if (window.addEventListener) { window.addEventListener("load", function () { nx_usain_beacon.send(); }, false) ; } } </script><noscript> <div class="noscript_disp" style="z-index:99; position:fixed;top:0px;left:0px;width:100%;padding:0 10px;background-color:#C00000 ;color:#fff;text-align:center;font-family:Helvetica, sans-serif"> <h1 style="font-size:16px">브라우저 설정에서 JavaScript를 켜주세요.</h1> </div> </noscript><div id="naver-splugin-wrap" style="display: none; user-select: text;"></div><div id="naver-splugin-dimmed" class="naver-splugin-dimmed spi_card_fixed_dimmed" style="display: none;"><div class="naver-splugin-dimmed spi_card_dimmed_inner"></div></div><iframe style="display: none;"></iframe></body></html>